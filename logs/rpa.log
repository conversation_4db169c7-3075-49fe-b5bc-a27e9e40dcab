[2025-07-08T16:33:22.304954Z] INFO - 执行异步最终清理
  Task ID: block_unblock_management_block_unblock_management_20250708_162616_993
  Business: block_unblock_management.block_unblock_management
  Step: async_final_cleanup
[2025-07-08T16:33:22.667704Z] INFO - Playwright驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_162616_993
  Business: block_unblock_management.block_unblock_management
  Step: driver_closed
  Details:
    operations: 3
    errors: 0
    screenshots: 0
    execution_time: 425.57769870758057
[2025-07-08T16:33:22.667704Z] INFO - Web驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_162616_993
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_cleanup
[2025-07-08T16:33:22.676514Z] INFO - 任务执行成功
[2025-07-08T16:40:09.446552Z] DEBUG - Using proactor: I<PERSON><PERSON><PERSON>roactor
[2025-07-08T16:40:09.447177Z] INFO - 启动屏蔽解屏蔽功能
[2025-07-08T16:40:09.449899Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:40:09.449899Z] INFO - 异步RPA脚本初始化完成: block_unblock_management.block_unblock_management
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_rpa_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    task_id: block_unblock_management_block_unblock_management_20250708_164009_447
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', 'PLAYWRIGHT_PAGE_LOAD_WAIT', 'PLAYWRIGHT_TIMEOUT', 'LOG_LEVEL', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-08T16:40:09.450427Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:40:09.450427Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:40:09.450970Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: yimai_login_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-08T16:40:09.450970Z] INFO - 通用Token提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.450970Z] INFO - 用户信息提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.450970Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:40:09.450970Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:40:09.451497Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:40:09.451497Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:40:09.451497Z] INFO - Excel数据处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.451497Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:40:09.451497Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:40:09.452034Z] INFO - RPA任务开始
  Task ID: defc72f8-4cb5-449c-a292-5ece30342a42
  Business: block_unblock_management.db_query_processor
  Step: task_start
[2025-07-08T16:40:09.554865Z] INFO - 创建数据库连接池: 127.0.0.1:3306/lingyi
  Task ID: defc72f8-4cb5-449c-a292-5ece30342a42
  Business: block_unblock_management.db_query_processor
  Step: create_pool
[2025-07-08T16:40:09.555865Z] INFO - 数据库管理器初始化完成
  Task ID: defc72f8-4cb5-449c-a292-5ece30342a42
  Business: block_unblock_management.db_query_processor
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-08T16:40:09.555865Z] INFO - 数据库操作器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    table_name: publish_success_list
    insert_fields_count: 32
    update_fields_count: 30
[2025-07-08T16:40:09.555865Z] INFO - 数据库查询处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.555865Z] INFO - RPA任务开始
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: task_start
[2025-07-08T16:40:09.555865Z] INFO - 日志增强器初始化完成
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: logging_enhancer_init
[2025-07-08T16:40:09.555865Z] INFO - 屏蔽管理器初始化完成（简化版-集成批量导入）
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: manager_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    mode: integrated_batch_import
    components_initialized: ['login_manager', 'token_extractor', 'user_info_extractor', 'notice_client', 'excel_processor', 'stock_client', 'db_query_processor', 'logging_enhancer']
[2025-07-08T16:40:09.565102Z] INFO - 屏蔽解屏蔽RPA初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.565102Z] INFO - 开始执行异步RPA任务
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_task_start
  Details:
    task_id: block_unblock_management_block_unblock_management_20250708_164009_447
    business_type: block_unblock_management
    script_name: block_unblock_management
[2025-07-08T16:40:09.565102Z] INFO - 执行异步前置验证
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_pre_validation
[2025-07-08T16:40:09.566179Z] INFO - 异步前置验证通过
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_pre_validation_passed
[2025-07-08T16:40:09.566703Z] INFO - 执行主要RPA逻辑
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: main_execution_start
[2025-07-08T16:40:09.566703Z] INFO - 开始执行屏蔽解屏蔽任务
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.566703Z] INFO - 查询时间范围: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:09.567768Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:40:09.567768Z] INFO - Playwright驱动管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: driver_init
  Details:
    browser_type: chromium
    headless: True
    viewport: {'width': 1920, 'height': 1080}
[2025-07-08T16:40:09.567768Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:40:09.568274Z] INFO - Web驱动创建成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: driver_factory
  Details:
    driver_type: playwright
    business_type: block_unblock_management
    script_name: block_unblock_management
    use_cache: True
    cache_key: playwright_block_unblock_management_block_unblock_management
[2025-07-08T16:40:09.568343Z] INFO - Web驱动实例已创建
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_created
  Details:
    driver_type: chromium
    headless: True
    timeout: 30
[2025-07-08T16:40:10.025875Z] INFO - Playwright驱动初始化成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: driver_initialized
  Details:
    browser_type: chromium
    context_options: {'viewport': {'width': 1920, 'height': 1080}, 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'ignore_https_errors': True, 'bypass_csp': True, 'java_script_enabled': True}
[2025-07-08T16:40:10.025875Z] INFO - 🚀 开始执行: 屏蔽解屏蔽完整流程（集成批量导入）
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: execution_start
  Details:
    trace_id: trace_1751964010_90aaaa5c
    step_name: 屏蔽解屏蔽完整流程（集成批量导入）
    start_time: 2025-07-08T16:40:10.025875
[2025-07-08T16:40:10.025875Z] INFO - 📍 检查点: 流程启动
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964010_90aaaa5c
    checkpoint_name: 流程启动
    checkpoint_data: {'date_range': ('2025-07-07 00:00:00', '2025-07-07 23:59:59'), 'driver_type': 'PlaywrightDriverManager', 'mode': 'integrated_import', 'config_version': 'unknown'}
    timestamp: 2025-07-08T16:40:10.025875
[2025-07-08T16:40:10.025875Z] INFO - 🚀 开始执行: 步骤1: 登录和信息提取
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: execution_start
  Details:
    trace_id: trace_1751964010_90aaaa5c
    step_name: 步骤1: 登录和信息提取
    start_time: 2025-07-08T16:40:10.025875
[2025-07-08T16:40:10.025875Z] INFO - 📍 检查点: 开始登录验证
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964010_90aaaa5c
    checkpoint_name: 开始登录验证
    checkpoint_data: {'driver_ready': True}
    timestamp: 2025-07-08T16:40:10.025875
[2025-07-08T16:40:10.029869Z] INFO - 已注册响应处理器，将拦截登录请求
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:10.029869Z] INFO - 开始确保亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:10.029869Z] INFO - 开始确保亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: ensure_login_start
[2025-07-08T16:40:10.029869Z] DEBUG - 检查亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: check_login_status
[2025-07-08T16:40:10.030868Z] DEBUG - 当前页面URL: about:blank
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: current_url_check
[2025-07-08T16:40:10.030868Z] DEBUG - 登录状态检查未通过，用户未登录
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: login_check_failed
[2025-07-08T16:40:10.030868Z] INFO - 用户未登录，开始执行登录操作
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: start_login
[2025-07-08T16:40:10.030868Z] INFO - 开始执行登录操作 (第1次尝试)
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: perform_login_start
[2025-07-08T16:40:10.032873Z] DEBUG - 网络请求拦截已启用
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:10.032873Z] INFO - 导航到登录页面: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: navigate_to_login
[2025-07-08T16:40:13.409732Z] INFO - 导航到URL成功: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: navigate
  Details:
    url: https://dcmmaster.yibainetwork.com/#/login_page
    wait_until: domcontentloaded
    timeout: 30000
[2025-07-08T16:40:13.415732Z] DEBUG - 页面加载完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: page_load
  Details:
    state: domcontentloaded
[2025-07-08T16:40:13.888226Z] DEBUG - 处理响应时异常: 'utf-8' codec can't decode byte 0x89 in position 0: invalid start byte
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:14.284512Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/Login/getWebConfig
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/Login/getWebConfig
    response_preview: {"code":200,"data":{"serviceProviderId":2,"useTo":2,"title":"亿迈商户系统","icon":"https:\/\/javafdcdn.yib...
[2025-07-08T16:40:15.002108Z] DEBUG - 处理响应时异常: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:28.415208Z] DEBUG - 等待页面完全加载
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: wait_page_ready
[2025-07-08T16:40:30.449121Z] INFO - 开始填写登录表单
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: fill_login_form
[2025-07-08T16:40:30.449642Z] DEBUG - 填写用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: fill_username
[2025-07-08T16:40:30.466661Z] DEBUG - 等待元素成功: input[placeholder*="用户名"], input[name="username"], input[type="text"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: wait_element
  Details:
    selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    normalized_selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    state: visible
[2025-07-08T16:40:30.568314Z] INFO - 输入文本成功: input[placeholder*="用户名"], input[name="username"], input[type="text"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: input
  Details:
    selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    normalized_selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    text_length: 11
    clear_first: True
[2025-07-08T16:40:30.568314Z] DEBUG - 填写密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: fill_password
[2025-07-08T16:40:30.578823Z] DEBUG - 等待元素成功: input[placeholder*="密码"], input[name="password"], input[type="password"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: wait_element
  Details:
    selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    normalized_selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    state: visible
[2025-07-08T16:40:30.650147Z] INFO - 输入文本成功: input[placeholder*="密码"], input[name="password"], input[type="password"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: input
  Details:
    selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    normalized_selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    text_length: 8
    clear_first: True
[2025-07-08T16:40:31.620597Z] INFO - 登录表单填写完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: fill_login_form_success
[2025-07-08T16:40:31.621597Z] INFO - 点击登录按钮
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: click_login_button
[2025-07-08T16:40:31.621597Z] DEBUG - 尝试登录按钮选择器 1: button.el-button--primary:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:40:31.645575Z] DEBUG - 尝试登录按钮选择器 2: button.el-button--primary.el-button--small:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:40:31.665208Z] DEBUG - 尝试登录按钮选择器 3: button[type='button'].el-button--primary:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:40:31.682208Z] DEBUG - 尝试登录按钮选择器 4: button:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:40:31.825272Z] INFO - 登录按钮点击成功，使用选择器: button:has-text('登录') (第1个匹配)
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: click_login_button_success
[2025-07-08T16:40:31.825272Z] INFO - 等待登录完成，最大等待时间: 30秒
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: wait_login_completion
[2025-07-08T16:40:31.826269Z] DEBUG - 登录前URL: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: initial_url
[2025-07-08T16:40:32.798615Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/Login/getOrganizationByAccount
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/Login/getOrganizationByAccount
    response_preview: {"code":200,"data":{"深圳奇点添财投资合伙企业（有限合伙）":"6057"},"msg":"操作成功","redirectUrl":null,"back":false,"refre...
[2025-07-08T16:40:32.799184Z] DEBUG - 拦截到登录相关请求: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:33.852084Z] INFO - 检测到页面标题变化: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: title_change_detected
[2025-07-08T16:40:33.852084Z] INFO - 登录操作执行成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: perform_login_success
[2025-07-08T16:40:33.852630Z] INFO - 验证登录结果
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: verify_login_success
[2025-07-08T16:40:35.870823Z] DEBUG - 登录后当前URL: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: post_login_url
[2025-07-08T16:40:35.871370Z] DEBUG - 登录后页面标题: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: post_login_title
[2025-07-08T16:40:35.871370Z] INFO - 页面标题验证登录成功: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: title_verification_success
[2025-07-08T16:40:35.871899Z] INFO - 亿迈系统登录成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: login_success
  Details:
    attempts: 1
    successes: 1
[2025-07-08T16:40:35.871899Z] INFO - 亿迈系统登录状态确认完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.872418Z] INFO - 开始提取认证Token
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.872428Z] INFO - 开始提取所有Token信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.872428Z] DEBUG - 开始提取JWT Token
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.878333Z] DEBUG - 找到Cookie POS_COOKIE_WEBFRONT_userdata: a%3A13%3A%7Bs%3A2%3A%22ip%22%3Bs%3A14%3A%2239.187....
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.879396Z] INFO - 从Cookie提取JWT Token成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    jwt_token_preview: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlU...
    jwt_token_length: 653
    source: cookie
[2025-07-08T16:40:35.879915Z] DEBUG - 开始提取Token1-Check
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.931711Z] WARNING - 未找到Token1-Check
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.932709Z] DEBUG - 开始提取设备指纹
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.973653Z] WARNING - 未找到设备指纹，使用默认值: 9fdbac6325f8d0bd62d79ad88fbedb8f
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.973653Z] DEBUG - 开始提取Token2信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:35.996304Z] DEBUG - 开始提取反爬虫验证码
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.048040Z] DEBUG - 未找到反爬虫验证码
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.048040Z] DEBUG - 开始提取签名
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.102221Z] WARNING - 未找到签名
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.102742Z] DEBUG - 开始提取Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.107739Z] INFO - 成功提取 1 个Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_names: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:40:36.107739Z] INFO - Token提取完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    has_jwt_token: True
    has_token1_check: False
    has_device_number: True
    has_token2: False
    has_anticlimb_code: False
    has_sign: False
    has_cookies: True
    cookie_count: 1
[2025-07-08T16:40:36.107739Z] INFO - Token提取完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    has_jwt_token: True
    jwt_token_length: 653
    has_token1_check: False
    has_device_number: True
    has_token2: False
    has_token2_timestamp: True
    has_anticlimb_verify_code: False
[2025-07-08T16:40:36.107739Z] DEBUG - 开始验证Token有效性
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.108738Z] INFO - Token验证通过
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.108738Z] INFO - 开始提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.108738Z] INFO - 尝试从网络历史记录中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.108738Z] INFO - 开始从网络历史记录中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.115741Z] WARNING - 网络历史记录中未找到登录响应
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.116739Z] INFO - 尝试从JWT Token中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.116739Z] INFO - 开始从JWT Token中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.116739Z] DEBUG - 成功解析JWT Token payload
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    available_fields: ['serviceProviderId', 'accountName', 'source', 'userName', 'uuid', 'authorityId', 'organizationCode', 'merchantId', 'orgCode', 'userType', 'relevanceId', 'exp', 'jti', 'fx_user_id', 'fx_user_name', 'fx_distributor_id']
[2025-07-08T16:40:36.117740Z] WARNING - JWT Token中未找到用户ID字段
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.117740Z] ERROR - 未能提取到用户信息，无法继续执行
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.117740Z] INFO - ✅ 执行成功: 步骤1: 登录和信息提取
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: execution_success
  Details:
    trace_id: trace_1751964010_90aaaa5c
    step_name: 步骤1: 登录和信息提取
    execution_time: 26.092s
    context: {'trace_id': 'trace_1751964010_90aaaa5c', 'step_name': '步骤1: 登录和信息提取', 'start_time': '2025-07-08T16:40:10.025875', 'end_time': '2025-07-08T16:40:36.117740', 'success': True, 'error_code': None, 'error_message': None, 'performance_metrics': {'execution_time': 26.091865301132202}, 'duration': 26.091865}
[2025-07-08T16:40:36.118739Z] INFO - 步骤2: 获取公告通知列表中的屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.118739Z] INFO - 🌐 正常模式：开始获取最新屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    date_range: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
[2025-07-08T16:40:36.118739Z] INFO - 🖥️ Windows本地环境：获取公告通知列表（无时间筛选）
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    local_mode: False
    is_local_windows: True
    page_size: 20
    title_keyword: 屏蔽与释放
[2025-07-08T16:40:36.118739Z] INFO - 开始构建请求参数
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    tokens_available: True
    tokens_keys: ['jwt_token', 'token1_check', 'device_number', 'token2', 'token2_timestamp', 'anticlimb_verify_code', 'sign', 'cookies']
    jwt_token_present: True
    jwt_token_length: 653
    auth_token_present: False
    token1_check_present: True
    token2_present: True
    token2_timestamp_present: True
    device_number_present: True
    local_mode: False
    is_local_windows: True
[2025-07-08T16:40:36.119740Z] DEBUG - 尝试从JWT Token解析用户ID
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    tokens_available: True
    has_jwt_token: True
    local_mode: False
    is_local_windows: True
[2025-07-08T16:40:36.119740Z] WARNING - JWT Token中未找到用户ID字段
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    available_fields: ['serviceProviderId', 'accountName', 'source', 'userName', 'uuid', 'authorityId', 'organizationCode', 'merchantId', 'orgCode', 'userType', 'relevanceId', 'exp', 'jti', 'fx_user_id', 'fx_user_name', 'fx_distributor_id']
[2025-07-08T16:40:36.119740Z] WARNING - JWT Token解析失败
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:36.120739Z] DEBUG - 🖥️ Windows本地环境：不添加时间筛选条件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    local_mode: False
    is_local_windows: True
    params_count: 12
    user_id: 
[2025-07-08T16:40:36.120739Z] DEBUG - 构建公告通知列表请求参数完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    params_count: 12
    date_range: N/A (无时间筛选)
    title_keyword: 屏蔽与释放
    user_id: 
    has_user_id: False
    local_mode: False
    is_local_windows: True
    has_time_filter: False
[2025-07-08T16:40:36.120739Z] INFO - 请求参数构建完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    uid_param: 
    uid_is_empty: True
    params_count: 12
    params_keys: ['read_status', 'title', 'type', 'is_top', 'size', 'distributor_id', 'msg_type', 'current', 'page', 'limit', 'uid', 'source_from']
    has_time_filter: False
    local_mode: False
    is_local_windows: True
[2025-07-08T16:40:36.120739Z] INFO - 准备调用公告通知列表接口
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    url: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    method: GET
    local_mode: False
    is_local_windows: True
[2025-07-08T16:40:36.121739Z] INFO - 执行GET请求 (第1次尝试)
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    params: {'read_status': '', 'title': '屏蔽与释放', 'type': '', 'is_top': '', 'size': 20, 'distributor_id': 6057, 'msg_type': 1, 'current': 1, 'page': 1, 'limit': 20, 'uid': '', 'source_from': 1}
    attempt: 1
[2025-07-08T16:40:36.121739Z] DEBUG - 请求头信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    headers_keys: ['accept', 'accept-encoding', 'accept-language', 'content-type', 'origin', 'referer', 'sec-ch-ua', 'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site', 'user-agent', 'Authorization', 'devicenumber', 'token1-check', 'token2', 'token2-timestamp', 'Scd']
    has_authorization: True
    auth_header_length: 653
[2025-07-08T16:40:36.121739Z] DEBUG - 使用Token中的Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_keys: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:40:38.870791Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/login/accountLogin
    response_preview: {"code":200,"data":{"session_id":"62687bfc2aaf1558104ca1fe654b913c","serviceProviderId":"2","token_i...
[2025-07-08T16:40:38.890150Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/login/accountLogin
    response_preview: {"code":200,"data":{"session_id":"62687bfc2aaf1558104ca1fe654b913c","serviceProviderId":"2","token_i...
[2025-07-08T16:40:38.913341Z] DEBUG - 成功解析登录响应数据，包含字段: ['code', 'data', 'msg', 'redirectUrl', 'back', 'refresh']
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:38.913341Z] INFO - 从登录响应中提取到用户ID: 19311
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.085474Z] INFO - GET请求成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    status: 200
[2025-07-08T16:40:39.086474Z] INFO - 公告通知列表接口调用成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    response_code: 200
    response_message: 
    has_data: True
    data_type: dict
    local_mode: False
    is_local_windows: True
[2025-07-08T16:40:39.086474Z] INFO - 解析公告列表响应成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    total_count: 26
    current_page: 1
    page_size: 20
    records_count: 20
[2025-07-08T16:40:39.087473Z] INFO - 获取到 20 条公告记录
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    records_count: 20
    mode: Windows本地环境
    local_mode: False
    is_local_windows: True
[2025-07-08T16:40:39.088474Z] INFO - 找到发送时间最新的屏蔽文件记录
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    total_records: 20
    valid_records_with_file_path: 20
    processed_records: 20
    latest_record_id: 686b81ce3e27345a340641df
    latest_time: 2025-07-07 16:14:06
    time_field_used: created_at
    has_file_path: True
[2025-07-08T16:40:39.088474Z] DEBUG - file_path是数组，取第一个元素: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.089473Z] INFO - ✅ 正常模式：获取最新屏蔽文件成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    file_path: ['https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx']
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    record_id: 686b81ce3e27345a340641df
    created_at: 2025-07-07 16:14:06
    local_mode: False
[2025-07-08T16:40:39.089473Z] INFO - 找到 1 个屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    date_range: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
[2025-07-08T16:40:39.089473Z] INFO - 步骤3: 下载并处理Excel文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.089473Z] INFO - 开始处理 1 个公告文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.090472Z] INFO - 开始下载Excel文件: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.090472Z] DEBUG - 文件URL是字符串: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.090472Z] INFO - 开始下载文件: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.090472Z] INFO - 开始获取文件内容: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:40:39.090472Z] DEBUG - 使用Token中的Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_keys: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:40:40.243863Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/datacenter/OrderSales/getUserCurrencyConfig?page_type=1&uid=19311&source_from=1
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/datacenter/OrderSales/getUserCurrencyConfig?page_type=1&uid=19311...
    response_preview: {"code":200,"data":{"currency_list":{"CNY":"人民币(￥)","USD":"美元($)","GBP":"英镑(￡)","EUR":"欧元(€)"},"user...
[2025-07-08T16:40:45.362238Z] DEBUG - 拦截到登录相关响应: https://api-c.soboten.com/text/chat-visit/user/load/v6?sysNum=eae4f163a02743709cff6a2e7d7ef8be&source=0&channelFlag=&platformUnionCode=&partnerId=***********&agid=&xst=&ucTrackUrl=&toTiao_clickId=&sogou_logidUrl=&visitTitle=%25E4%25BA%25BF%25E8%25BF%2588%25E5%2595%2586%25E6%2588%25B7%25E7%25B3%25BB%25E7%25BB%259F&visitUrl=&visitStartTime=&ack=1&chooseAdminId=&tranFlag=0&groupId=&tel=&email=&uname=%E6%B7%B1%E5%9C%B3%E5%A5%87%E7%82%B9%E6%B7%BB%E8%B4%A2%E6%8A%95%E8%B5%84%E5%90%88%E4%BC%99%E4%BC%81%E4%B8%9A%EF%BC%88%E6%9C%89%E9%99%90%E5%90%88%E4%BC%99%EF%BC%89%E6%9D%8E%E5%8B%A4&face=&realname=%E6%9D%8E%E5%8B%A4&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&isReComment=1&customerFields=%7B%22customField6%22%3A%22%E6%B7%B1%E5%9C%B3%E5%A5%87%E7%82%B9%E6%B7%BB%E8%B4%A2%E6%8A%95%E8%B5%84%E5%90%88%E4%BC%99%E4%BC%81%E4%B8%9A%EF%BC%88%E6%9C%89%E9%99%90%E5%90%88%E4%BC%99%EF%BC%89%22%2C%22customField8%22%3A%22***********%22%2C%22customField9%22%3A%22%E6%99%AE%E9%80%9A%E7%94%A8%E6%88%B7%22%2C%22customField10%22%3A%22VIP2%22%7D&multiParams=&summaryParams=&isVip=&vipLevel=&userLabel=&isJs=1&joinType=&callback=callback1751964043383
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://api-c.soboten.com/text/chat-visit/user/load/v6?sysNum=eae4f163a02743709cff6a2e7d7ef8be&sourc...
    response_preview: callback1751964043383({"consultWindowStyle":1,"whiteListFlag":0,"color":"#0DAEAF,#0DAEAF","firstTime...
[2025-07-08T16:42:39.891717Z] ERROR - 文件内容获取异常: 
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    exception_type: TimeoutError
[2025-07-08T16:42:39.891717Z] ERROR - 下载文件失败: 
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
[2025-07-08T16:42:39.892712Z] ERROR - 处理文件失败: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx - 
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.896292Z] INFO - 公告文件处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus_count: 0
    unblock_skus_count: 0
    files_processed: 0
[2025-07-08T16:42:39.896292Z] INFO - Excel文件处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus: 0
    unblock_skus: 0
[2025-07-08T16:42:39.897290Z] INFO - 步骤4: 查询数据库获取店铺信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.897290Z] WARNING - 没有SKU需要查询
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.897290Z] INFO - 🚀 开始集成批量导入模式
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    enriched_data_keys: ['block_skus', 'unblock_skus', 'processed_files', 'statistics', 'database_mapping']
    has_database_mapping: True
[2025-07-08T16:42:39.897290Z] INFO - 数据库映射转换完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus: 0
    unblock_skus: 0
    total_business_records: 0
[2025-07-08T16:42:39.897290Z] ERROR - 集成批量导入失败: 转换后的业务数据为空
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.897290Z] INFO - 📍 检查点: 流程失败
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964010_90aaaa5c
    checkpoint_name: 流程失败
    checkpoint_data: {'error_type': 'ValueError', 'error_message': '转换后的业务数据为空', 'date_range': ('2025-07-07 00:00:00', '2025-07-07 23:59:59')}
    timestamp: 2025-07-08T16:42:39.897290
[2025-07-08T16:42:39.897290Z] INFO - 🧹 开始清理临时文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.937351Z] INFO - RPA任务开始
  Task ID: block_unblock_management_integrated_batch_importer_20250708_164239_936
  Business: block_unblock_management.integrated_batch_importer
  Step: task_start
[2025-07-08T16:42:39.937351Z] INFO - 异步RPA脚本初始化完成: block_unblock_management.integrated_batch_importer
  Task ID: block_unblock_management_integrated_batch_importer_20250708_164239_936
  Business: block_unblock_management.integrated_batch_importer
  Step: async_rpa_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    task_id: block_unblock_management_integrated_batch_importer_20250708_164239_936
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', 'PLAYWRIGHT_PAGE_LOAD_WAIT', 'PLAYWRIGHT_TIMEOUT', 'LOG_LEVEL', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-08T16:42:39.937351Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:42:39.937351Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:42:39.937351Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:42:39.938339Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:42:39.938339Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: yimai_login_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-08T16:42:39.938986Z] INFO - 通用Token提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.938986Z] INFO - 用户信息提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.938986Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:42:39.938986Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:42:39.940983Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:42:39.940983Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:42:39.940983Z] INFO - Excel数据处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.940983Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:42:39.940983Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:42:39.940983Z] INFO - RPA任务开始
  Task ID: 082c7fdc-7b68-4738-b0a3-350dc345c013
  Business: block_unblock_management.db_query_processor
  Step: task_start
[2025-07-08T16:42:39.941983Z] INFO - 数据库管理器初始化完成
  Task ID: 082c7fdc-7b68-4738-b0a3-350dc345c013
  Business: block_unblock_management.db_query_processor
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-08T16:42:39.941983Z] INFO - 数据库操作器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    table_name: publish_success_list
    insert_fields_count: 32
    update_fields_count: 30
[2025-07-08T16:42:39.941983Z] INFO - 数据库查询处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.941983Z] INFO - RPA任务开始
  Task ID: e0425131-e264-41a9-82f4-fb7fe38eccab
  Business: block_unblock_management.logging_enhancer
  Step: task_start
[2025-07-08T16:42:39.941983Z] INFO - 日志增强器初始化完成
  Task ID: e0425131-e264-41a9-82f4-fb7fe38eccab
  Business: block_unblock_management.logging_enhancer
  Step: logging_enhancer_init
[2025-07-08T16:42:39.941983Z] INFO - 屏蔽管理器初始化完成（简化版-集成批量导入）
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: manager_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    mode: integrated_batch_import
    components_initialized: ['login_manager', 'token_extractor', 'user_info_extractor', 'notice_client', 'excel_processor', 'stock_client', 'db_query_processor', 'logging_enhancer']
[2025-07-08T16:42:39.941983Z] INFO - 集成批量导入处理器初始化完成（优化版）
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: importer_init
  Details:
    template_config: {'official_url': 'https://salecentersaasapi.yibainetwork.com//template//amazon//import_amazon_stock_adjust_template.xlsx', 'max_file_size': 52428800, 'download_timeout': 60}
    batch_config: {'max_records_per_file': 9999, 'import_interval_min': 3, 'import_interval_max': 5, 'max_concurrent_imports': 1, 'retry_failed_imports': True, 'max_retries': 2}
    auth_config: {'max_auth_retries': 2, 'auth_retry_delay': 3, 'token_check_interval': 300}
    rpa_config: {'enabled': False, 'deprecated': True, 'recommended_alternative': 'API调用模式', 'navigation_timeout': 30000, 'element_timeout': 15000, 'upload_timeout': 30000, 'cross_platform_upload': True, 'retry_on_navigation_failure': True, 'auto_login_on_redirect': True}
    platform: Windows
    platform_version: Windows-10-10.0.19045-SP0
[2025-07-08T16:42:39.941983Z] INFO - 🧹 开始清理集成批量导入临时文件（跨平台）
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    platform: Windows
[2025-07-08T16:42:39.953361Z] INFO - 🧹 临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    success: True
    cleaned_files: 0
    cleaned_dirs: 0
    errors: []
    platform: Windows
    temp_base_dir: C:\Users\<USER>\AppData\Local\Temp
    patterns_checked: ['batch_import_*', 'official_template_*.xlsx', 'integrated_*', '批量导入_*.xlsx', 'template_*_cleaned.xlsx']
[2025-07-08T16:42:39.953361Z] INFO - ✅ 集成批量导入临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    cleaned_files: 0
    cleaned_dirs: 0
[2025-07-08T16:42:39.957688Z] INFO - ✅ 临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Details:
    total_cleaned: 0
    temp_dir: C:\Users\<USER>\AppData\Local\Temp
[2025-07-08T16:42:39.958687Z] INFO - ✅ 执行成功: 屏蔽解屏蔽完整流程（集成批量导入）
  Task ID: e1991cf0-23ec-4ba2-8138-5917fe6b836f
  Business: block_unblock_management.logging_enhancer
  Step: execution_success
  Details:
    trace_id: trace_1751964010_90aaaa5c
    step_name: 屏蔽解屏蔽完整流程（集成批量导入）
    execution_time: 149.932s
    context: {'trace_id': 'trace_1751964010_90aaaa5c', 'step_name': '屏蔽解屏蔽完整流程（集成批量导入）', 'start_time': '2025-07-08T16:40:10.025875', 'end_time': '2025-07-08T16:42:39.957688', 'success': True, 'error_code': None, 'error_message': None, 'performance_metrics': {'execution_time': 149.93181347846985}, 'duration': 149.931813}
[2025-07-08T16:42:39.958687Z] ERROR - 屏蔽解屏蔽任务执行失败: 屏蔽解屏蔽流程执行失败: 转换后的业务数据为空
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.960687Z] DEBUG - 没有找到需要清理的临时文件（Excel处理都在内存中）
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:42:39.960687Z] INFO - 步骤执行完成: main_execution
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: main_execution_end
  Execution Time: 150.395s
[2025-07-08T16:42:39.960687Z] INFO - 执行异步后置处理
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_post_processing
  Details:
    result_keys: ['success', 'message', 'error']
    result_size: 79
[2025-07-08T16:42:39.961686Z] INFO - 异步后置处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_post_processing_complete
[2025-07-08T16:42:39.961686Z] INFO - RPA任务执行完成 - 状态: success
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: task_summary
  Details:
    status: success
    total_execution_time: 150.39658451080322
    start_time: 2025-07-08T08:40:09.449872Z
    end_time: 2025-07-08T08:42:39.961686Z
    result: {'success': False, 'message': '屏蔽解屏蔽流程执行失败: 转换后的业务数据为空', 'error': '转换后的业务数据为空'}
    operation_count: 0
    error_count: 0
    retry_count: 0
    step_times: {}
  Execution Time: 150.512s
[2025-07-08T16:42:39.961686Z] INFO - 执行异步最终清理
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: async_final_cleanup
[2025-07-08T16:42:40.378413Z] INFO - Playwright驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: driver_closed
  Details:
    operations: 3
    errors: 0
    screenshots: 0
    execution_time: 150.81006932258606
[2025-07-08T16:42:40.378413Z] INFO - Web驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_164009_447
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_cleanup
[2025-07-08T16:42:40.378413Z] INFO - 任务执行成功
[2025-07-08T16:43:23.572746Z] DEBUG - Using proactor: IocpProactor
[2025-07-08T16:43:23.573283Z] INFO - 启动屏蔽解屏蔽功能
[2025-07-08T16:43:23.574858Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:43:23.574858Z] INFO - 异步RPA脚本初始化完成: block_unblock_management.block_unblock_management
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_rpa_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    task_id: block_unblock_management_block_unblock_management_20250708_164323_573
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', 'PLAYWRIGHT_PAGE_LOAD_WAIT', 'PLAYWRIGHT_TIMEOUT', 'LOG_LEVEL', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-08T16:43:23.575859Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:43:23.575859Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:43:23.575859Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: yimai_login_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-08T16:43:23.575859Z] INFO - 通用Token提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.575859Z] INFO - 用户信息提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.575859Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:43:23.576858Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:43:23.576858Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:43:23.576858Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:43:23.576858Z] INFO - Excel数据处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.576858Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:43:23.576858Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:43:23.577858Z] INFO - RPA任务开始
  Task ID: bfb9b256-5865-41d5-8ce1-a319929da09a
  Business: block_unblock_management.db_query_processor
  Step: task_start
[2025-07-08T16:43:23.613723Z] INFO - 创建数据库连接池: 127.0.0.1:3306/lingyi
  Task ID: bfb9b256-5865-41d5-8ce1-a319929da09a
  Business: block_unblock_management.db_query_processor
  Step: create_pool
[2025-07-08T16:43:23.613723Z] INFO - 数据库管理器初始化完成
  Task ID: bfb9b256-5865-41d5-8ce1-a319929da09a
  Business: block_unblock_management.db_query_processor
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-08T16:43:23.614251Z] INFO - 数据库操作器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    table_name: publish_success_list
    insert_fields_count: 32
    update_fields_count: 30
[2025-07-08T16:43:23.614251Z] INFO - 数据库查询处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.614251Z] INFO - RPA任务开始
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: task_start
[2025-07-08T16:43:23.614251Z] INFO - 日志增强器初始化完成
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: logging_enhancer_init
[2025-07-08T16:43:23.614251Z] INFO - 屏蔽管理器初始化完成（简化版-集成批量导入）
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: manager_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    mode: integrated_batch_import
    components_initialized: ['login_manager', 'token_extractor', 'user_info_extractor', 'notice_client', 'excel_processor', 'stock_client', 'db_query_processor', 'logging_enhancer']
[2025-07-08T16:43:23.621337Z] INFO - 屏蔽解屏蔽RPA初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.621337Z] INFO - 开始执行异步RPA任务
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_task_start
  Details:
    task_id: block_unblock_management_block_unblock_management_20250708_164323_573
    business_type: block_unblock_management
    script_name: block_unblock_management
[2025-07-08T16:43:23.621337Z] INFO - 执行异步前置验证
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_pre_validation
[2025-07-08T16:43:23.622335Z] INFO - 异步前置验证通过
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_pre_validation_passed
[2025-07-08T16:43:23.622335Z] INFO - 执行主要RPA逻辑
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: main_execution_start
[2025-07-08T16:43:23.622335Z] INFO - 开始执行屏蔽解屏蔽任务
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.623330Z] INFO - 查询时间范围: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:23.624337Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:43:23.624337Z] INFO - Playwright驱动管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: driver_init
  Details:
    browser_type: chromium
    headless: True
    viewport: {'width': 1920, 'height': 1080}
[2025-07-08T16:43:23.624337Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:43:23.624337Z] INFO - Web驱动创建成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: driver_factory
  Details:
    driver_type: playwright
    business_type: block_unblock_management
    script_name: block_unblock_management
    use_cache: True
    cache_key: playwright_block_unblock_management_block_unblock_management
[2025-07-08T16:43:23.624337Z] INFO - Web驱动实例已创建
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_created
  Details:
    driver_type: chromium
    headless: True
    timeout: 30
[2025-07-08T16:43:24.079403Z] INFO - Playwright驱动初始化成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: driver_initialized
  Details:
    browser_type: chromium
    context_options: {'viewport': {'width': 1920, 'height': 1080}, 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'ignore_https_errors': True, 'bypass_csp': True, 'java_script_enabled': True}
[2025-07-08T16:43:24.079937Z] INFO - 🚀 开始执行: 屏蔽解屏蔽完整流程（集成批量导入）
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: execution_start
  Details:
    trace_id: trace_1751964204_60859bba
    step_name: 屏蔽解屏蔽完整流程（集成批量导入）
    start_time: 2025-07-08T16:43:24.079937
[2025-07-08T16:43:24.079937Z] INFO - 📍 检查点: 流程启动
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964204_60859bba
    checkpoint_name: 流程启动
    checkpoint_data: {'date_range': ('2025-07-07 00:00:00', '2025-07-07 23:59:59'), 'driver_type': 'PlaywrightDriverManager', 'mode': 'integrated_import', 'config_version': 'unknown'}
    timestamp: 2025-07-08T16:43:24.079937
[2025-07-08T16:43:24.079937Z] INFO - 🚀 开始执行: 步骤1: 登录和信息提取
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: execution_start
  Details:
    trace_id: trace_1751964204_60859bba
    step_name: 步骤1: 登录和信息提取
    start_time: 2025-07-08T16:43:24.079937
[2025-07-08T16:43:24.080473Z] INFO - 📍 检查点: 开始登录验证
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964204_60859bba
    checkpoint_name: 开始登录验证
    checkpoint_data: {'driver_ready': True}
    timestamp: 2025-07-08T16:43:24.079937
[2025-07-08T16:43:24.091421Z] INFO - 已注册响应处理器，将拦截登录请求
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:24.091421Z] INFO - 开始确保亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:24.091421Z] INFO - 开始确保亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: ensure_login_start
[2025-07-08T16:43:24.091421Z] DEBUG - 检查亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: check_login_status
[2025-07-08T16:43:24.091421Z] DEBUG - 当前页面URL: about:blank
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: current_url_check
[2025-07-08T16:43:24.091957Z] DEBUG - 登录状态检查未通过，用户未登录
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: login_check_failed
[2025-07-08T16:43:24.091957Z] INFO - 用户未登录，开始执行登录操作
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: start_login
[2025-07-08T16:43:24.091957Z] INFO - 开始执行登录操作 (第1次尝试)
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: perform_login_start
[2025-07-08T16:43:24.094113Z] DEBUG - 网络请求拦截已启用
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:24.094645Z] INFO - 导航到登录页面: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: navigate_to_login
[2025-07-08T16:43:27.461842Z] INFO - 导航到URL成功: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: navigate
  Details:
    url: https://dcmmaster.yibainetwork.com/#/login_page
    wait_until: domcontentloaded
    timeout: 30000
[2025-07-08T16:43:27.467299Z] DEBUG - 页面加载完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: page_load
  Details:
    state: domcontentloaded
[2025-07-08T16:43:28.497576Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/Login/getWebConfig
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/Login/getWebConfig
    response_preview: {"code":200,"data":{"serviceProviderId":2,"useTo":2,"title":"亿迈商户系统","icon":"https:\/\/javafdcdn.yib...
[2025-07-08T16:43:28.769420Z] DEBUG - 处理响应时异常: 'utf-8' codec can't decode byte 0x89 in position 0: invalid start byte
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:29.043461Z] DEBUG - 处理响应时异常: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:42.474418Z] DEBUG - 等待页面完全加载
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: wait_page_ready
[2025-07-08T16:43:44.494606Z] INFO - 开始填写登录表单
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: fill_login_form
[2025-07-08T16:43:44.494606Z] DEBUG - 填写用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: fill_username
[2025-07-08T16:43:44.507958Z] DEBUG - 等待元素成功: input[placeholder*="用户名"], input[name="username"], input[type="text"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: wait_element
  Details:
    selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    normalized_selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    state: visible
[2025-07-08T16:43:44.566205Z] INFO - 输入文本成功: input[placeholder*="用户名"], input[name="username"], input[type="text"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: input
  Details:
    selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    normalized_selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    text_length: 11
    clear_first: True
[2025-07-08T16:43:44.566205Z] DEBUG - 填写密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: fill_password
[2025-07-08T16:43:44.572206Z] DEBUG - 等待元素成功: input[placeholder*="密码"], input[name="password"], input[type="password"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: wait_element
  Details:
    selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    normalized_selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    state: visible
[2025-07-08T16:43:44.611015Z] INFO - 输入文本成功: input[placeholder*="密码"], input[name="password"], input[type="password"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: input
  Details:
    selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    normalized_selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    text_length: 8
    clear_first: True
[2025-07-08T16:43:45.609089Z] INFO - 登录表单填写完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: fill_login_form_success
[2025-07-08T16:43:45.609089Z] INFO - 点击登录按钮
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: click_login_button
[2025-07-08T16:43:45.610083Z] DEBUG - 尝试登录按钮选择器 1: button.el-button--primary:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:43:45.622192Z] DEBUG - 尝试登录按钮选择器 2: button.el-button--primary.el-button--small:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:43:45.632446Z] DEBUG - 尝试登录按钮选择器 3: button[type='button'].el-button--primary:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:43:45.640453Z] DEBUG - 尝试登录按钮选择器 4: button:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:43:45.739139Z] INFO - 登录按钮点击成功，使用选择器: button:has-text('登录') (第1个匹配)
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: click_login_button_success
[2025-07-08T16:43:45.739139Z] INFO - 等待登录完成，最大等待时间: 30秒
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: wait_login_completion
[2025-07-08T16:43:45.739139Z] DEBUG - 登录前URL: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: initial_url
[2025-07-08T16:43:46.080922Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/Login/getOrganizationByAccount
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/Login/getOrganizationByAccount
    response_preview: {"code":200,"data":{"深圳奇点添财投资合伙企业（有限合伙）":"6057"},"msg":"操作成功","redirectUrl":null,"back":false,"refre...
[2025-07-08T16:43:46.082727Z] DEBUG - 拦截到登录相关请求: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:47.772866Z] INFO - 检测到页面标题变化: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: title_change_detected
[2025-07-08T16:43:47.773412Z] INFO - 登录操作执行成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: perform_login_success
[2025-07-08T16:43:47.773412Z] INFO - 验证登录结果
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: verify_login_success
[2025-07-08T16:43:49.220275Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/login/accountLogin
    response_preview: {"code":200,"data":{"session_id":"58e5ef188e141c23de133c0149902f6b","serviceProviderId":"2","token_i...
[2025-07-08T16:43:49.229767Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/login/accountLogin
    response_preview: {"code":200,"data":{"session_id":"58e5ef188e141c23de133c0149902f6b","serviceProviderId":"2","token_i...
[2025-07-08T16:43:49.243674Z] DEBUG - 成功解析登录响应数据，包含字段: ['code', 'data', 'msg', 'redirectUrl', 'back', 'refresh']
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.244190Z] INFO - 从登录响应中提取到用户ID: 19311
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.658661Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/datacenter/OrderSales/getUserCurrencyConfig?page_type=1&uid=19311&source_from=1
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/datacenter/OrderSales/getUserCurrencyConfig?page_type=1&uid=19311...
    response_preview: {"code":200,"data":{"currency_list":{"CNY":"人民币(￥)","USD":"美元($)","GBP":"英镑(￡)","EUR":"欧元(€)"},"user...
[2025-07-08T16:43:49.760525Z] DEBUG - 登录后当前URL: https://dcmmaster.yibainetwork.com/#/home_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: post_login_url
[2025-07-08T16:43:49.760525Z] DEBUG - 登录后页面标题: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: post_login_title
[2025-07-08T16:43:49.760525Z] INFO - 页面标题验证登录成功: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: title_verification_success
[2025-07-08T16:43:49.760525Z] INFO - 亿迈系统登录成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: login_success
  Details:
    attempts: 1
    successes: 1
[2025-07-08T16:43:49.760525Z] INFO - 亿迈系统登录状态确认完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.761527Z] INFO - 开始提取认证Token
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.761527Z] INFO - 开始提取所有Token信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.761527Z] DEBUG - 开始提取JWT Token
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.764533Z] DEBUG - 找到Cookie POS_COOKIE_WEBFRONT_userdata: a%3A13%3A%7Bs%3A2%3A%22ip%22%3Bs%3A14%3A%2239.187....
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.765526Z] INFO - 从Cookie提取JWT Token成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    jwt_token_preview: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlU...
    jwt_token_length: 653
    source: cookie
[2025-07-08T16:43:49.765526Z] DEBUG - 开始提取Token1-Check
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.780017Z] DEBUG - 拦截到登录相关响应: https://api-c.soboten.com/text/chat-visit/user/load/v6?sysNum=eae4f163a02743709cff6a2e7d7ef8be&source=0&channelFlag=&platformUnionCode=&partnerId=***********&agid=&xst=&ucTrackUrl=&toTiao_clickId=&sogou_logidUrl=&visitTitle=%25E4%25BA%25BF%25E8%25BF%2588%25E5%2595%2586%25E6%2588%25B7%25E7%25B3%25BB%25E7%25BB%259F&visitUrl=&visitStartTime=&ack=1&chooseAdminId=&tranFlag=0&groupId=&tel=&email=&uname=%E6%B7%B1%E5%9C%B3%E5%A5%87%E7%82%B9%E6%B7%BB%E8%B4%A2%E6%8A%95%E8%B5%84%E5%90%88%E4%BC%99%E4%BC%81%E4%B8%9A%EF%BC%88%E6%9C%89%E9%99%90%E5%90%88%E4%BC%99%EF%BC%89%E6%9D%8E%E5%8B%A4&face=&realname=%E6%9D%8E%E5%8B%A4&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&isReComment=1&customerFields=%7B%22customField6%22%3A%22%E6%B7%B1%E5%9C%B3%E5%A5%87%E7%82%B9%E6%B7%BB%E8%B4%A2%E6%8A%95%E8%B5%84%E5%90%88%E4%BC%99%E4%BC%81%E4%B8%9A%EF%BC%88%E6%9C%89%E9%99%90%E5%90%88%E4%BC%99%EF%BC%89%22%2C%22customField8%22%3A%22***********%22%2C%22customField9%22%3A%22%E6%99%AE%E9%80%9A%E7%94%A8%E6%88%B7%22%2C%22customField10%22%3A%22VIP2%22%7D&multiParams=&summaryParams=&isVip=&vipLevel=&userLabel=&isJs=1&joinType=&callback=callback1751964229430
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://api-c.soboten.com/text/chat-visit/user/load/v6?sysNum=eae4f163a02743709cff6a2e7d7ef8be&sourc...
    response_preview: callback1751964229430({"consultWindowStyle":1,"whiteListFlag":0,"color":"#0DAEAF,#0DAEAF","firstTime...
[2025-07-08T16:43:49.800378Z] WARNING - 未找到Token1-Check
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.800378Z] DEBUG - 开始提取设备指纹
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.819644Z] WARNING - 未找到设备指纹，使用默认值: d6ecd574b348eb692f95bdf4fb12393f
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.819644Z] DEBUG - 开始提取Token2信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.834026Z] DEBUG - 开始提取反爬虫验证码
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.863807Z] DEBUG - 未找到反爬虫验证码
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.863807Z] DEBUG - 开始提取签名
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.891553Z] WARNING - 未找到签名
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.892129Z] DEBUG - 开始提取Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.894910Z] INFO - 成功提取 1 个Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_names: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:43:49.894910Z] INFO - Token提取完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    has_jwt_token: True
    has_token1_check: False
    has_device_number: True
    has_token2: False
    has_anticlimb_code: False
    has_sign: False
    has_cookies: True
    cookie_count: 1
[2025-07-08T16:43:49.895441Z] INFO - Token提取完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    has_jwt_token: True
    jwt_token_length: 653
    has_token1_check: False
    has_device_number: True
    has_token2: False
    has_token2_timestamp: True
    has_anticlimb_verify_code: False
[2025-07-08T16:43:49.895441Z] DEBUG - 开始验证Token有效性
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.895441Z] INFO - Token验证通过
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.895441Z] INFO - 开始提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.895957Z] INFO - 使用已提取的用户信息，用户ID: 19311
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.895967Z] INFO - 用户信息提取成功，用户ID: 19311
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    user_id: 19311
    account: None
    account_name: None
[2025-07-08T16:43:49.895967Z] INFO - ✅ 执行成功: 步骤1: 登录和信息提取
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: execution_success
  Details:
    trace_id: trace_1751964204_60859bba
    step_name: 步骤1: 登录和信息提取
    execution_time: 25.816s
    context: {'trace_id': 'trace_1751964204_60859bba', 'step_name': '步骤1: 登录和信息提取', 'start_time': '2025-07-08T16:43:24.079937', 'end_time': '2025-07-08T16:43:49.895967', 'success': True, 'error_code': None, 'error_message': None, 'performance_metrics': {'execution_time': 25.***************}, 'duration': 25.81603}
[2025-07-08T16:43:49.895967Z] INFO - 步骤2: 获取公告通知列表中的屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:49.896481Z] INFO - 🌐 正常模式：开始获取最新屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    date_range: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
[2025-07-08T16:43:49.897025Z] INFO - 🖥️ Windows本地环境：获取公告通知列表（无时间筛选）
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    local_mode: False
    is_local_windows: True
    page_size: 20
    title_keyword: 屏蔽与释放
[2025-07-08T16:43:49.897025Z] INFO - 开始构建请求参数
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    tokens_available: True
    tokens_keys: ['jwt_token', 'token1_check', 'device_number', 'token2', 'token2_timestamp', 'anticlimb_verify_code', 'sign', 'cookies']
    jwt_token_present: True
    jwt_token_length: 653
    auth_token_present: False
    token1_check_present: True
    token2_present: True
    token2_timestamp_present: True
    device_number_present: True
    local_mode: False
    is_local_windows: True
[2025-07-08T16:43:49.897025Z] DEBUG - 使用传入的用户ID
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    user_id: 19311
    source: parameter
    local_mode: False
    is_local_windows: True
[2025-07-08T16:43:49.897558Z] DEBUG - 🖥️ Windows本地环境：不添加时间筛选条件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    local_mode: False
    is_local_windows: True
    params_count: 12
    user_id: 19311
[2025-07-08T16:43:49.897558Z] DEBUG - 构建公告通知列表请求参数完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    params_count: 12
    date_range: N/A (无时间筛选)
    title_keyword: 屏蔽与释放
    user_id: 19311
    has_user_id: True
    local_mode: False
    is_local_windows: True
    has_time_filter: False
[2025-07-08T16:43:49.897558Z] INFO - 请求参数构建完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    uid_param: 19311
    uid_is_empty: False
    params_count: 12
    params_keys: ['read_status', 'title', 'type', 'is_top', 'size', 'distributor_id', 'msg_type', 'current', 'page', 'limit', 'uid', 'source_from']
    has_time_filter: False
    local_mode: False
    is_local_windows: True
[2025-07-08T16:43:49.897558Z] INFO - 准备调用公告通知列表接口
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    url: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    method: GET
    local_mode: False
    is_local_windows: True
[2025-07-08T16:43:49.897558Z] INFO - 执行GET请求 (第1次尝试)
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    params: {'read_status': '', 'title': '屏蔽与释放', 'type': '', 'is_top': '', 'size': 20, 'distributor_id': 6057, 'msg_type': 1, 'current': 1, 'page': 1, 'limit': 20, 'uid': '19311', 'source_from': 1}
    attempt: 1
[2025-07-08T16:43:49.897558Z] DEBUG - 请求头信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    headers_keys: ['accept', 'accept-encoding', 'accept-language', 'content-type', 'origin', 'referer', 'sec-ch-ua', 'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site', 'user-agent', 'Authorization', 'devicenumber', 'token1-check', 'token2', 'token2-timestamp', 'Scd']
    has_authorization: True
    auth_header_length: 653
[2025-07-08T16:43:49.898065Z] DEBUG - 使用Token中的Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_keys: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:43:50.490787Z] INFO - GET请求成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    status: 200
[2025-07-08T16:43:50.491780Z] INFO - 公告通知列表接口调用成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    response_code: 200
    response_message: 
    has_data: True
    data_type: dict
    local_mode: False
    is_local_windows: True
[2025-07-08T16:43:50.491780Z] INFO - 解析公告列表响应成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    total_count: 26
    current_page: 1
    page_size: 20
    records_count: 20
[2025-07-08T16:43:50.491780Z] INFO - 获取到 20 条公告记录
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    records_count: 20
    mode: Windows本地环境
    local_mode: False
    is_local_windows: True
[2025-07-08T16:43:50.491780Z] INFO - 找到发送时间最新的屏蔽文件记录
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    total_records: 20
    valid_records_with_file_path: 20
    processed_records: 20
    latest_record_id: 686b81ce3e27345a340641df
    latest_time: 2025-07-07 16:14:06
    time_field_used: created_at
    has_file_path: True
[2025-07-08T16:43:50.492782Z] DEBUG - file_path是数组，取第一个元素: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.492782Z] INFO - ✅ 正常模式：获取最新屏蔽文件成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    file_path: ['https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx']
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    record_id: 686b81ce3e27345a340641df
    created_at: 2025-07-07 16:14:06
    local_mode: False
[2025-07-08T16:43:50.492782Z] INFO - 找到 1 个屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    date_range: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
[2025-07-08T16:43:50.492782Z] INFO - 步骤3: 下载并处理Excel文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.492782Z] INFO - 开始处理 1 个公告文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.492782Z] INFO - 开始下载Excel文件: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.496410Z] DEBUG - 文件URL是字符串: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.496985Z] INFO - 开始下载文件: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.496985Z] INFO - 开始获取文件内容: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:43:50.496985Z] DEBUG - 使用Token中的Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_keys: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:45:50.884998Z] ERROR - 文件内容获取异常: 
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    exception_type: TimeoutError
[2025-07-08T16:45:50.884998Z] ERROR - 下载文件失败: 
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
[2025-07-08T16:45:50.884998Z] ERROR - 处理文件失败: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx - 
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.892988Z] INFO - 公告文件处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus_count: 0
    unblock_skus_count: 0
    files_processed: 0
[2025-07-08T16:45:50.892988Z] INFO - Excel文件处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus: 0
    unblock_skus: 0
[2025-07-08T16:45:50.892988Z] INFO - 步骤4: 查询数据库获取店铺信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.892988Z] WARNING - 没有SKU需要查询
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.892988Z] INFO - 🚀 开始集成批量导入模式
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    enriched_data_keys: ['block_skus', 'unblock_skus', 'processed_files', 'statistics', 'database_mapping']
    has_database_mapping: True
[2025-07-08T16:45:50.892988Z] INFO - 数据库映射转换完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus: 0
    unblock_skus: 0
    total_business_records: 0
[2025-07-08T16:45:50.892988Z] ERROR - 集成批量导入失败: 转换后的业务数据为空
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.892988Z] INFO - 📍 检查点: 流程失败
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964204_60859bba
    checkpoint_name: 流程失败
    checkpoint_data: {'error_type': 'ValueError', 'error_message': '转换后的业务数据为空', 'date_range': ('2025-07-07 00:00:00', '2025-07-07 23:59:59')}
    timestamp: 2025-07-08T16:45:50.892988
[2025-07-08T16:45:50.892988Z] INFO - 🧹 开始清理临时文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.918990Z] INFO - RPA任务开始
  Task ID: block_unblock_management_integrated_batch_importer_20250708_164550_918
  Business: block_unblock_management.integrated_batch_importer
  Step: task_start
[2025-07-08T16:45:50.919996Z] INFO - 异步RPA脚本初始化完成: block_unblock_management.integrated_batch_importer
  Task ID: block_unblock_management_integrated_batch_importer_20250708_164550_918
  Business: block_unblock_management.integrated_batch_importer
  Step: async_rpa_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    task_id: block_unblock_management_integrated_batch_importer_20250708_164550_918
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', 'PLAYWRIGHT_PAGE_LOAD_WAIT', 'PLAYWRIGHT_TIMEOUT', 'LOG_LEVEL', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-08T16:45:50.919996Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:45:50.919996Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:45:50.919996Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:45:50.919996Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:45:50.919996Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: yimai_login_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-08T16:45:50.920989Z] INFO - 通用Token提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.920989Z] INFO - 用户信息提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.920989Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:45:50.920989Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:45:50.920989Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:45:50.920989Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:45:50.920989Z] INFO - Excel数据处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.921988Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:45:50.921988Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:45:50.921988Z] INFO - RPA任务开始
  Task ID: 8d18325e-01cc-48f2-9f72-645a47f2fa0d
  Business: block_unblock_management.db_query_processor
  Step: task_start
[2025-07-08T16:45:50.921988Z] INFO - 数据库管理器初始化完成
  Task ID: 8d18325e-01cc-48f2-9f72-645a47f2fa0d
  Business: block_unblock_management.db_query_processor
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-08T16:45:50.921988Z] INFO - 数据库操作器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    table_name: publish_success_list
    insert_fields_count: 32
    update_fields_count: 30
[2025-07-08T16:45:50.921988Z] INFO - 数据库查询处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.922988Z] INFO - RPA任务开始
  Task ID: fcc97268-c23e-4b5f-9fed-f86d43bb3bec
  Business: block_unblock_management.logging_enhancer
  Step: task_start
[2025-07-08T16:45:50.922988Z] INFO - 日志增强器初始化完成
  Task ID: fcc97268-c23e-4b5f-9fed-f86d43bb3bec
  Business: block_unblock_management.logging_enhancer
  Step: logging_enhancer_init
[2025-07-08T16:45:50.922988Z] INFO - 屏蔽管理器初始化完成（简化版-集成批量导入）
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: manager_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    mode: integrated_batch_import
    components_initialized: ['login_manager', 'token_extractor', 'user_info_extractor', 'notice_client', 'excel_processor', 'stock_client', 'db_query_processor', 'logging_enhancer']
[2025-07-08T16:45:50.922988Z] INFO - 集成批量导入处理器初始化完成（优化版）
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: importer_init
  Details:
    template_config: {'official_url': 'https://salecentersaasapi.yibainetwork.com//template//amazon//import_amazon_stock_adjust_template.xlsx', 'max_file_size': 52428800, 'download_timeout': 60}
    batch_config: {'max_records_per_file': 9999, 'import_interval_min': 3, 'import_interval_max': 5, 'max_concurrent_imports': 1, 'retry_failed_imports': True, 'max_retries': 2}
    auth_config: {'max_auth_retries': 2, 'auth_retry_delay': 3, 'token_check_interval': 300}
    rpa_config: {'enabled': False, 'deprecated': True, 'recommended_alternative': 'API调用模式', 'navigation_timeout': 30000, 'element_timeout': 15000, 'upload_timeout': 30000, 'cross_platform_upload': True, 'retry_on_navigation_failure': True, 'auto_login_on_redirect': True}
    platform: Windows
    platform_version: Windows-10-10.0.19045-SP0
[2025-07-08T16:45:50.922988Z] INFO - 🧹 开始清理集成批量导入临时文件（跨平台）
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    platform: Windows
[2025-07-08T16:45:50.931987Z] INFO - 🧹 临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    success: True
    cleaned_files: 0
    cleaned_dirs: 0
    errors: []
    platform: Windows
    temp_base_dir: C:\Users\<USER>\AppData\Local\Temp
    patterns_checked: ['batch_import_*', 'official_template_*.xlsx', 'integrated_*', '批量导入_*.xlsx', 'template_*_cleaned.xlsx']
[2025-07-08T16:45:50.932988Z] INFO - ✅ 集成批量导入临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    cleaned_files: 0
    cleaned_dirs: 0
[2025-07-08T16:45:50.936988Z] INFO - ✅ 临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Details:
    total_cleaned: 0
    temp_dir: C:\Users\<USER>\AppData\Local\Temp
[2025-07-08T16:45:50.936988Z] INFO - ✅ 执行成功: 屏蔽解屏蔽完整流程（集成批量导入）
  Task ID: a1664392-246f-47cc-9dd1-************
  Business: block_unblock_management.logging_enhancer
  Step: execution_success
  Details:
    trace_id: trace_1751964204_60859bba
    step_name: 屏蔽解屏蔽完整流程（集成批量导入）
    execution_time: 146.857s
    context: {'trace_id': 'trace_1751964204_60859bba', 'step_name': '屏蔽解屏蔽完整流程（集成批量导入）', 'start_time': '2025-07-08T16:43:24.079937', 'end_time': '2025-07-08T16:45:50.936988', 'success': True, 'error_code': None, 'error_message': None, 'performance_metrics': {'execution_time': 146.85705161094666}, 'duration': 146.857051}
[2025-07-08T16:45:50.937987Z] ERROR - 屏蔽解屏蔽任务执行失败: 屏蔽解屏蔽流程执行失败: 转换后的业务数据为空
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.939997Z] DEBUG - 没有找到需要清理的临时文件（Excel处理都在内存中）
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:45:50.939997Z] INFO - 步骤执行完成: main_execution
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: main_execution_end
  Execution Time: 147.318s
[2025-07-08T16:45:50.939997Z] INFO - 执行异步后置处理
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_post_processing
  Details:
    result_keys: ['success', 'message', 'error']
    result_size: 79
[2025-07-08T16:45:50.939997Z] INFO - 异步后置处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_post_processing_complete
[2025-07-08T16:45:50.939997Z] INFO - RPA任务执行完成 - 状态: success
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: task_summary
  Details:
    status: success
    total_execution_time: 147.3186595439911
    start_time: 2025-07-08T08:43:23.574858Z
    end_time: 2025-07-08T08:45:50.939997Z
    result: {'success': False, 'message': '屏蔽解屏蔽流程执行失败: 转换后的业务数据为空', 'error': '转换后的业务数据为空'}
    operation_count: 0
    error_count: 0
    retry_count: 0
    step_times: {}
  Execution Time: 147.365s
[2025-07-08T16:45:50.939997Z] INFO - 执行异步最终清理
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: async_final_cleanup
[2025-07-08T16:45:51.484737Z] INFO - Playwright驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: driver_closed
  Details:
    operations: 3
    errors: 0
    screenshots: 0
    execution_time: 147.86040019989014
[2025-07-08T16:45:51.485282Z] INFO - Web驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_164323_573
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_cleanup
[2025-07-08T16:45:51.485282Z] INFO - 任务执行成功
[2025-07-08T16:46:03.764280Z] DEBUG - Using proactor: IocpProactor
[2025-07-08T16:46:03.764851Z] INFO - 启动屏蔽解屏蔽功能
[2025-07-08T16:46:03.766456Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:46:03.766980Z] INFO - 异步RPA脚本初始化完成: block_unblock_management.block_unblock_management
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_rpa_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    task_id: block_unblock_management_block_unblock_management_20250708_164603_764
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', 'PLAYWRIGHT_PAGE_LOAD_WAIT', 'PLAYWRIGHT_TIMEOUT', 'LOG_LEVEL', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-08T16:46:03.766980Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:46:03.766980Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:46:03.767501Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: yimai_login_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-08T16:46:03.767501Z] INFO - 通用Token提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.767501Z] INFO - 用户信息提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.768021Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:46:03.768021Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:46:03.768021Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:46:03.768021Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:46:03.768021Z] INFO - Excel数据处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.768021Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:46:03.768527Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:46:03.768548Z] INFO - RPA任务开始
  Task ID: d616fd54-fdbd-450b-b52f-7762040f3039
  Business: block_unblock_management.db_query_processor
  Step: task_start
[2025-07-08T16:46:03.904693Z] INFO - 创建数据库连接池: 127.0.0.1:3306/lingyi
  Task ID: d616fd54-fdbd-450b-b52f-7762040f3039
  Business: block_unblock_management.db_query_processor
  Step: create_pool
[2025-07-08T16:46:03.904693Z] INFO - 数据库管理器初始化完成
  Task ID: d616fd54-fdbd-450b-b52f-7762040f3039
  Business: block_unblock_management.db_query_processor
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-08T16:46:03.904693Z] INFO - 数据库操作器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    table_name: publish_success_list
    insert_fields_count: 32
    update_fields_count: 30
[2025-07-08T16:46:03.904693Z] INFO - 数据库查询处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.904693Z] INFO - RPA任务开始
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: task_start
[2025-07-08T16:46:03.904693Z] INFO - 日志增强器初始化完成
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: logging_enhancer_init
[2025-07-08T16:46:03.904693Z] INFO - 屏蔽管理器初始化完成（简化版-集成批量导入）
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: manager_init
  Details:
    business_type: block_unblock_management
    script_name: block_unblock_management
    mode: integrated_batch_import
    components_initialized: ['login_manager', 'token_extractor', 'user_info_extractor', 'notice_client', 'excel_processor', 'stock_client', 'db_query_processor', 'logging_enhancer']
[2025-07-08T16:46:03.911695Z] INFO - 屏蔽解屏蔽RPA初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.912693Z] INFO - 开始执行异步RPA任务
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_task_start
  Details:
    task_id: block_unblock_management_block_unblock_management_20250708_164603_764
    business_type: block_unblock_management
    script_name: block_unblock_management
[2025-07-08T16:46:03.912693Z] INFO - 执行异步前置验证
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_pre_validation
[2025-07-08T16:46:03.913706Z] INFO - 异步前置验证通过
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_pre_validation_passed
[2025-07-08T16:46:03.913706Z] INFO - 执行主要RPA逻辑
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: main_execution_start
[2025-07-08T16:46:03.913706Z] INFO - 开始执行屏蔽解屏蔽任务
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.913706Z] INFO - 查询时间范围: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:03.914694Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:46:03.914694Z] INFO - Playwright驱动管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: driver_init
  Details:
    browser_type: chromium
    headless: True
    viewport: {'width': 1920, 'height': 1080}
[2025-07-08T16:46:03.915693Z] INFO - RPA任务开始
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: task_start
[2025-07-08T16:46:03.915693Z] INFO - Web驱动创建成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: driver_factory
  Details:
    driver_type: playwright
    business_type: block_unblock_management
    script_name: block_unblock_management
    use_cache: True
    cache_key: playwright_block_unblock_management_block_unblock_management
[2025-07-08T16:46:03.915693Z] INFO - Web驱动实例已创建
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_created
  Details:
    driver_type: chromium
    headless: True
    timeout: 30
[2025-07-08T16:46:04.377639Z] INFO - Playwright驱动初始化成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: driver_initialized
  Details:
    browser_type: chromium
    context_options: {'viewport': {'width': 1920, 'height': 1080}, 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'ignore_https_errors': True, 'bypass_csp': True, 'java_script_enabled': True}
[2025-07-08T16:46:04.378636Z] INFO - 🚀 开始执行: 屏蔽解屏蔽完整流程（集成批量导入）
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: execution_start
  Details:
    trace_id: trace_1751964364_6185ea03
    step_name: 屏蔽解屏蔽完整流程（集成批量导入）
    start_time: 2025-07-08T16:46:04.377639
[2025-07-08T16:46:04.378636Z] INFO - 📍 检查点: 流程启动
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964364_6185ea03
    checkpoint_name: 流程启动
    checkpoint_data: {'date_range': ('2025-07-07 00:00:00', '2025-07-07 23:59:59'), 'driver_type': 'PlaywrightDriverManager', 'mode': 'integrated_import', 'config_version': 'unknown'}
    timestamp: 2025-07-08T16:46:04.378636
[2025-07-08T16:46:04.378636Z] INFO - 🚀 开始执行: 步骤1: 登录和信息提取
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: execution_start
  Details:
    trace_id: trace_1751964364_6185ea03
    step_name: 步骤1: 登录和信息提取
    start_time: 2025-07-08T16:46:04.378636
[2025-07-08T16:46:04.378636Z] INFO - 📍 检查点: 开始登录验证
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964364_6185ea03
    checkpoint_name: 开始登录验证
    checkpoint_data: {'driver_ready': True}
    timestamp: 2025-07-08T16:46:04.378636
[2025-07-08T16:46:04.383725Z] INFO - 已注册响应处理器，将拦截登录请求
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:04.383725Z] INFO - 开始确保亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:04.383725Z] INFO - 开始确保亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: ensure_login_start
[2025-07-08T16:46:04.384723Z] DEBUG - 检查亿迈系统登录状态
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: check_login_status
[2025-07-08T16:46:04.384723Z] DEBUG - 当前页面URL: about:blank
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: current_url_check
[2025-07-08T16:46:04.384723Z] DEBUG - 登录状态检查未通过，用户未登录
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: login_check_failed
[2025-07-08T16:46:04.384723Z] INFO - 用户未登录，开始执行登录操作
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: start_login
[2025-07-08T16:46:04.384723Z] INFO - 开始执行登录操作 (第1次尝试)
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: perform_login_start
[2025-07-08T16:46:04.386724Z] DEBUG - 网络请求拦截已启用
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:04.387722Z] INFO - 导航到登录页面: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: navigate_to_login
[2025-07-08T16:46:22.257117Z] INFO - 导航到URL成功: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: navigate
  Details:
    url: https://dcmmaster.yibainetwork.com/#/login_page
    wait_until: domcontentloaded
    timeout: 30000
[2025-07-08T16:46:22.265806Z] DEBUG - 页面加载完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: page_load
  Details:
    state: domcontentloaded
[2025-07-08T16:46:25.164888Z] DEBUG - 处理响应时异常: 'utf-8' codec can't decode byte 0x89 in position 0: invalid start byte
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:26.308976Z] DEBUG - 处理响应时异常: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:26.309976Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/Login/getWebConfig
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/Login/getWebConfig
    response_preview: {"code":200,"data":{"serviceProviderId":2,"useTo":2,"title":"亿迈商户系统","icon":"https:\/\/javafdcdn.yib...
[2025-07-08T16:46:37.286776Z] DEBUG - 等待页面完全加载
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: wait_page_ready
[2025-07-08T16:46:39.311936Z] INFO - 开始填写登录表单
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: fill_login_form
[2025-07-08T16:46:39.311936Z] DEBUG - 填写用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: fill_username
[2025-07-08T16:46:39.322106Z] DEBUG - 等待元素成功: input[placeholder*="用户名"], input[name="username"], input[type="text"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: wait_element
  Details:
    selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    normalized_selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    state: visible
[2025-07-08T16:46:39.378244Z] INFO - 输入文本成功: input[placeholder*="用户名"], input[name="username"], input[type="text"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: input
  Details:
    selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    normalized_selector: input[placeholder*="用户名"], input[name="username"], input[type="text"]
    text_length: 11
    clear_first: True
[2025-07-08T16:46:39.378244Z] DEBUG - 填写密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: fill_password
[2025-07-08T16:46:39.385257Z] DEBUG - 等待元素成功: input[placeholder*="密码"], input[name="password"], input[type="password"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: wait_element
  Details:
    selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    normalized_selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    state: visible
[2025-07-08T16:46:39.426133Z] INFO - 输入文本成功: input[placeholder*="密码"], input[name="password"], input[type="password"]
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: input
  Details:
    selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    normalized_selector: input[placeholder*="密码"], input[name="password"], input[type="password"]
    text_length: 8
    clear_first: True
[2025-07-08T16:46:40.430235Z] INFO - 登录表单填写完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: fill_login_form_success
[2025-07-08T16:46:40.430748Z] INFO - 点击登录按钮
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: click_login_button
[2025-07-08T16:46:40.430759Z] DEBUG - 尝试登录按钮选择器 1: button.el-button--primary:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:46:40.440999Z] DEBUG - 尝试登录按钮选择器 2: button.el-button--primary.el-button--small:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:46:40.451590Z] DEBUG - 尝试登录按钮选择器 3: button[type='button'].el-button--primary:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:46:40.462198Z] DEBUG - 尝试登录按钮选择器 4: button:has-text('登录')
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: try_login_selector
[2025-07-08T16:46:40.566354Z] INFO - 登录按钮点击成功，使用选择器: button:has-text('登录') (第1个匹配)
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: click_login_button_success
[2025-07-08T16:46:40.566354Z] INFO - 等待登录完成，最大等待时间: 30秒
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: wait_login_completion
[2025-07-08T16:46:40.566354Z] DEBUG - 登录前URL: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: initial_url
[2025-07-08T16:46:41.908164Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/Login/getOrganizationByAccount
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/Login/getOrganizationByAccount
    response_preview: {"code":200,"data":{"深圳奇点添财投资合伙企业（有限合伙）":"6057"},"msg":"操作成功","redirectUrl":null,"back":false,"refre...
[2025-07-08T16:46:41.909667Z] DEBUG - 拦截到登录相关请求: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:42.627852Z] INFO - 检测到页面标题变化: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: title_change_detected
[2025-07-08T16:46:42.627852Z] INFO - 登录操作执行成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: perform_login_success
[2025-07-08T16:46:42.628385Z] INFO - 验证登录结果
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: verify_login_success
[2025-07-08T16:46:44.637574Z] DEBUG - 登录后当前URL: https://dcmmaster.yibainetwork.com/#/login_page
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: post_login_url
[2025-07-08T16:46:44.637574Z] DEBUG - 登录后页面标题: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: post_login_title
[2025-07-08T16:46:44.637574Z] INFO - 页面标题验证登录成功: 亿迈商户系统
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: title_verification_success
[2025-07-08T16:46:44.637574Z] INFO - 亿迈系统登录成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: login_success
  Details:
    attempts: 1
    successes: 1
[2025-07-08T16:46:44.637574Z] INFO - 亿迈系统登录状态确认完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.637574Z] INFO - 开始提取认证Token
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.637574Z] INFO - 开始提取所有Token信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.638576Z] DEBUG - 开始提取JWT Token
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.640575Z] DEBUG - 找到Cookie POS_COOKIE_WEBFRONT_userdata: a%3A13%3A%7Bs%3A2%3A%22ip%22%3Bs%3A14%3A%2239.187....
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.641576Z] INFO - 从Cookie提取JWT Token成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    jwt_token_preview: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlU...
    jwt_token_length: 653
    source: cookie
[2025-07-08T16:46:44.641576Z] DEBUG - 开始提取Token1-Check
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.669493Z] WARNING - 未找到Token1-Check
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.669493Z] DEBUG - 开始提取设备指纹
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.689012Z] WARNING - 未找到设备指纹，使用默认值: 72fae80e30dc202eae4ae7ce655899cc
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.689012Z] DEBUG - 开始提取Token2信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.702431Z] DEBUG - 开始提取反爬虫验证码
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.728305Z] DEBUG - 未找到反爬虫验证码
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.728305Z] DEBUG - 开始提取签名
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.753757Z] WARNING - 未找到签名
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.753757Z] DEBUG - 开始提取Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.755766Z] INFO - 成功提取 1 个Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_names: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:46:44.755766Z] INFO - Token提取完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    has_jwt_token: True
    has_token1_check: False
    has_device_number: True
    has_token2: False
    has_anticlimb_code: False
    has_sign: False
    has_cookies: True
    cookie_count: 1
[2025-07-08T16:46:44.755766Z] INFO - Token提取完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    has_jwt_token: True
    jwt_token_length: 653
    has_token1_check: False
    has_device_number: True
    has_token2: False
    has_token2_timestamp: True
    has_anticlimb_verify_code: False
[2025-07-08T16:46:44.755766Z] DEBUG - 开始验证Token有效性
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.756758Z] INFO - Token验证通过
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.756758Z] INFO - 开始提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.756758Z] INFO - 尝试从网络历史记录中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.756758Z] INFO - 开始从网络历史记录中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.760070Z] WARNING - 网络历史记录中未找到登录响应
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.760070Z] INFO - 尝试从JWT Token中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.760070Z] INFO - 开始从JWT Token中提取用户信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.760596Z] DEBUG - 成功解析JWT Token payload
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    available_fields: ['serviceProviderId', 'accountName', 'source', 'userName', 'uuid', 'authorityId', 'organizationCode', 'merchantId', 'orgCode', 'userType', 'relevanceId', 'exp', 'jti', 'fx_user_id', 'fx_user_name', 'fx_distributor_id']
[2025-07-08T16:46:44.760596Z] WARNING - JWT Token中未找到用户ID字段
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.760596Z] ERROR - 未能提取到用户信息，无法继续执行
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.761121Z] INFO - ✅ 执行成功: 步骤1: 登录和信息提取
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: execution_success
  Details:
    trace_id: trace_1751964364_6185ea03
    step_name: 步骤1: 登录和信息提取
    execution_time: 40.382s
    context: {'trace_id': 'trace_1751964364_6185ea03', 'step_name': '步骤1: 登录和信息提取', 'start_time': '2025-07-08T16:46:04.378636', 'end_time': '2025-07-08T16:46:44.760596', 'success': True, 'error_code': None, 'error_message': None, 'performance_metrics': {'execution_time': 40.381959676742554}, 'duration': 40.38196}
[2025-07-08T16:46:44.761121Z] INFO - 步骤2: 获取公告通知列表中的屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.761121Z] INFO - 🌐 正常模式：开始获取最新屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    date_range: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
[2025-07-08T16:46:44.761121Z] INFO - 🖥️ Windows本地环境：获取公告通知列表（无时间筛选）
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    local_mode: False
    is_local_windows: True
    page_size: 20
    title_keyword: 屏蔽与释放
[2025-07-08T16:46:44.761651Z] INFO - 开始构建请求参数
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    tokens_available: True
    tokens_keys: ['jwt_token', 'token1_check', 'device_number', 'token2', 'token2_timestamp', 'anticlimb_verify_code', 'sign', 'cookies']
    jwt_token_present: True
    jwt_token_length: 653
    auth_token_present: False
    token1_check_present: True
    token2_present: True
    token2_timestamp_present: True
    device_number_present: True
    local_mode: False
    is_local_windows: True
[2025-07-08T16:46:44.761651Z] DEBUG - 尝试从JWT Token解析用户ID
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    tokens_available: True
    has_jwt_token: True
    local_mode: False
    is_local_windows: True
[2025-07-08T16:46:44.761651Z] WARNING - JWT Token中未找到用户ID字段
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    available_fields: ['serviceProviderId', 'accountName', 'source', 'userName', 'uuid', 'authorityId', 'organizationCode', 'merchantId', 'orgCode', 'userType', 'relevanceId', 'exp', 'jti', 'fx_user_id', 'fx_user_name', 'fx_distributor_id']
[2025-07-08T16:46:44.761651Z] WARNING - JWT Token解析失败
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:44.761651Z] DEBUG - 🖥️ Windows本地环境：不添加时间筛选条件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    local_mode: False
    is_local_windows: True
    params_count: 12
    user_id: 
[2025-07-08T16:46:44.762183Z] DEBUG - 构建公告通知列表请求参数完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    params_count: 12
    date_range: N/A (无时间筛选)
    title_keyword: 屏蔽与释放
    user_id: 
    has_user_id: False
    local_mode: False
    is_local_windows: True
    has_time_filter: False
[2025-07-08T16:46:44.762183Z] INFO - 请求参数构建完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    uid_param: 
    uid_is_empty: True
    params_count: 12
    params_keys: ['read_status', 'title', 'type', 'is_top', 'size', 'distributor_id', 'msg_type', 'current', 'page', 'limit', 'uid', 'source_from']
    has_time_filter: False
    local_mode: False
    is_local_windows: True
[2025-07-08T16:46:44.762183Z] INFO - 准备调用公告通知列表接口
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    url: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    method: GET
    local_mode: False
    is_local_windows: True
[2025-07-08T16:46:44.762183Z] INFO - 执行GET请求 (第1次尝试)
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    params: {'read_status': '', 'title': '屏蔽与释放', 'type': '', 'is_top': '', 'size': 20, 'distributor_id': 6057, 'msg_type': 1, 'current': 1, 'page': 1, 'limit': 20, 'uid': '', 'source_from': 1}
    attempt: 1
[2025-07-08T16:46:44.762183Z] DEBUG - 请求头信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    headers_keys: ['accept', 'accept-encoding', 'accept-language', 'content-type', 'origin', 'referer', 'sec-ch-ua', 'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site', 'user-agent', 'Authorization', 'devicenumber', 'token1-check', 'token2', 'token2-timestamp', 'Scd']
    has_authorization: True
    auth_header_length: 653
[2025-07-08T16:46:44.762718Z] DEBUG - 使用Token中的Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_keys: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:46:48.252812Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/login/accountLogin
    response_preview: {"code":200,"data":{"session_id":"db221309e631d14a884a048bd377a487","serviceProviderId":"2","token_i...
[2025-07-08T16:46:48.263021Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/login/login/accountLogin
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/login/login/accountLogin
    response_preview: {"code":200,"data":{"session_id":"db221309e631d14a884a048bd377a487","serviceProviderId":"2","token_i...
[2025-07-08T16:46:48.275384Z] DEBUG - 成功解析登录响应数据，包含字段: ['code', 'data', 'msg', 'redirectUrl', 'back', 'refresh']
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:48.275384Z] INFO - 从登录响应中提取到用户ID: 19311
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.887111Z] INFO - GET请求成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: https://dcmmaster.yibainetwork.com/message/message/getMessageList
    status: 200
[2025-07-08T16:46:49.887637Z] INFO - 公告通知列表接口调用成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    response_code: 200
    response_message: 
    has_data: True
    data_type: dict
    local_mode: False
    is_local_windows: True
[2025-07-08T16:46:49.887637Z] INFO - 解析公告列表响应成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    total_count: 26
    current_page: 1
    page_size: 20
    records_count: 20
[2025-07-08T16:46:49.887637Z] INFO - 获取到 20 条公告记录
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    records_count: 20
    mode: Windows本地环境
    local_mode: False
    is_local_windows: True
[2025-07-08T16:46:49.888168Z] INFO - 找到发送时间最新的屏蔽文件记录
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    total_records: 20
    valid_records_with_file_path: 20
    processed_records: 20
    latest_record_id: 686b81ce3e27345a340641df
    latest_time: 2025-07-07 16:14:06
    time_field_used: created_at
    has_file_path: True
[2025-07-08T16:46:49.888168Z] DEBUG - file_path是数组，取第一个元素: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.888676Z] INFO - ✅ 正常模式：获取最新屏蔽文件成功
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    file_path: ['https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx']
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    record_id: 686b81ce3e27345a340641df
    created_at: 2025-07-07 16:14:06
    local_mode: False
[2025-07-08T16:46:49.888702Z] INFO - 找到 1 个屏蔽文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    date_range: ('2025-07-07 00:00:00', '2025-07-07 23:59:59')
[2025-07-08T16:46:49.888702Z] INFO - 步骤3: 下载并处理Excel文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.888702Z] INFO - 开始处理 1 个公告文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.888702Z] INFO - 开始下载Excel文件: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.890355Z] DEBUG - 文件URL是字符串: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.890355Z] INFO - 开始下载文件: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.890355Z] INFO - 开始获取文件内容: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:46:49.890355Z] DEBUG - 使用Token中的Cookie
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    cookie_keys: ['POS_COOKIE_WEBFRONT_userdata']
[2025-07-08T16:46:50.388403Z] DEBUG - 拦截到登录相关响应: https://dcmmaster.yibainetwork.com/datacenter/OrderSales/getUserCurrencyConfig?page_type=1&uid=19311&source_from=1
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://dcmmaster.yibainetwork.com/datacenter/OrderSales/getUserCurrencyConfig?page_type=1&uid=19311...
    response_preview: {"code":200,"data":{"currency_list":{"CNY":"人民币(￥)","USD":"美元($)","GBP":"英镑(￡)","EUR":"欧元(€)"},"user...
[2025-07-08T16:47:00.400435Z] DEBUG - 拦截到登录相关响应: https://api-c.soboten.com/text/chat-visit/user/load/v6?sysNum=eae4f163a02743709cff6a2e7d7ef8be&source=0&channelFlag=&platformUnionCode=&partnerId=***********&agid=&xst=&ucTrackUrl=&toTiao_clickId=&sogou_logidUrl=&visitTitle=%25E4%25BA%25BF%25E8%25BF%2588%25E5%2595%2586%25E6%2588%25B7%25E7%25B3%25BB%25E7%25BB%259F&visitUrl=&visitStartTime=&ack=1&chooseAdminId=&tranFlag=0&groupId=&tel=&email=&uname=%E6%B7%B1%E5%9C%B3%E5%A5%87%E7%82%B9%E6%B7%BB%E8%B4%A2%E6%8A%95%E8%B5%84%E5%90%88%E4%BC%99%E4%BC%81%E4%B8%9A%EF%BC%88%E6%9C%89%E9%99%90%E5%90%88%E4%BC%99%EF%BC%89%E6%9D%8E%E5%8B%A4&face=&realname=%E6%9D%8E%E5%8B%A4&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&isReComment=1&customerFields=%7B%22customField6%22%3A%22%E6%B7%B1%E5%9C%B3%E5%A5%87%E7%82%B9%E6%B7%BB%E8%B4%A2%E6%8A%95%E8%B5%84%E5%90%88%E4%BC%99%E4%BC%81%E4%B8%9A%EF%BC%88%E6%9C%89%E9%99%90%E5%90%88%E4%BC%99%EF%BC%89%22%2C%22customField8%22%3A%22***********%22%2C%22customField9%22%3A%22%E6%99%AE%E9%80%9A%E7%94%A8%E6%88%B7%22%2C%22customField10%22%3A%22VIP2%22%7D&multiParams=&summaryParams=&isVip=&vipLevel=&userLabel=&isJs=1&joinType=&callback=callback1751964417556
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    status: 200
    url: https://api-c.soboten.com/text/chat-visit/user/load/v6?sysNum=eae4f163a02743709cff6a2e7d7ef8be&sourc...
    response_preview: callback1751964417556({"consultWindowStyle":1,"whiteListFlag":0,"color":"#0DAEAF,#0DAEAF","firstTime...
[2025-07-08T16:48:50.878889Z] ERROR - 文件内容获取异常: 
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
    exception_type: TimeoutError
[2025-07-08T16:48:50.878889Z] ERROR - 下载文件失败: 
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    file_url: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx
[2025-07-08T16:48:50.878889Z] ERROR - 处理文件失败: https://saaserp-manage.yibainetwork.com/end/upload/sys_notice/20250707153906/屏蔽与释放2025-07-07.xlsx - 
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.885887Z] INFO - 公告文件处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus_count: 0
    unblock_skus_count: 0
    files_processed: 0
[2025-07-08T16:48:50.885887Z] INFO - Excel文件处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus: 0
    unblock_skus: 0
[2025-07-08T16:48:50.885887Z] INFO - 步骤4: 查询数据库获取店铺信息
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.886886Z] WARNING - 没有SKU需要查询
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.886886Z] INFO - 🚀 开始集成批量导入模式
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    enriched_data_keys: ['block_skus', 'unblock_skus', 'processed_files', 'statistics', 'database_mapping']
    has_database_mapping: True
[2025-07-08T16:48:50.886886Z] INFO - 数据库映射转换完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    block_skus: 0
    unblock_skus: 0
    total_business_records: 0
[2025-07-08T16:48:50.886886Z] ERROR - 集成批量导入失败: 转换后的业务数据为空
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.886886Z] INFO - 📍 检查点: 流程失败
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: checkpoint
  Details:
    trace_id: trace_1751964364_6185ea03
    checkpoint_name: 流程失败
    checkpoint_data: {'error_type': 'ValueError', 'error_message': '转换后的业务数据为空', 'date_range': ('2025-07-07 00:00:00', '2025-07-07 23:59:59')}
    timestamp: 2025-07-08T16:48:50.886886
[2025-07-08T16:48:50.886886Z] INFO - 🧹 开始清理临时文件
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.913887Z] INFO - RPA任务开始
  Task ID: block_unblock_management_integrated_batch_importer_20250708_164850_912
  Business: block_unblock_management.integrated_batch_importer
  Step: task_start
[2025-07-08T16:48:50.913887Z] INFO - 异步RPA脚本初始化完成: block_unblock_management.integrated_batch_importer
  Task ID: block_unblock_management_integrated_batch_importer_20250708_164850_912
  Business: block_unblock_management.integrated_batch_importer
  Step: async_rpa_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    task_id: block_unblock_management_integrated_batch_importer_20250708_164850_912
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', 'PLAYWRIGHT_PAGE_LOAD_WAIT', 'PLAYWRIGHT_TIMEOUT', 'LOG_LEVEL', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-08T16:48:50.913887Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:48:50.914885Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:48:50.914885Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:48:50.914885Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: config_load
[2025-07-08T16:48:50.914885Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: yimai_login_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-08T16:48:50.914885Z] INFO - 通用Token提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.914885Z] INFO - 用户信息提取器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.915886Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:48:50.915886Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:48:50.917894Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:48:50.917894Z] INFO - 公告通知客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    endpoint: /message/message/getMessageList
    title_keyword: 屏蔽与释放
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-08T16:48:50.917894Z] INFO - Excel数据处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.917894Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    base_url: https://salecentersaasapi.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-08T16:48:50.917894Z] INFO - 库存管理客户端初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: stock_client_init
  Details:
    endpoints: ['batch_import', 'import_status', 'import_result']
    upload_config: {'max_file_size': 52428800, 'timeout': 300, 'retry_count': 3, 'retry_delay': 2}
[2025-07-08T16:48:50.918893Z] INFO - RPA任务开始
  Task ID: a15cae4d-c6f4-4b9c-b080-6886da3eb9d0
  Business: block_unblock_management.db_query_processor
  Step: task_start
[2025-07-08T16:48:50.918893Z] INFO - 数据库管理器初始化完成
  Task ID: a15cae4d-c6f4-4b9c-b080-6886da3eb9d0
  Business: block_unblock_management.db_query_processor
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-08T16:48:50.918893Z] INFO - 数据库操作器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    table_name: publish_success_list
    insert_fields_count: 32
    update_fields_count: 30
[2025-07-08T16:48:50.918893Z] INFO - 数据库查询处理器初始化完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.918893Z] INFO - RPA任务开始
  Task ID: 95ae4bc4-de7b-487a-b4e5-005955aa42b1
  Business: block_unblock_management.logging_enhancer
  Step: task_start
[2025-07-08T16:48:50.918893Z] INFO - 日志增强器初始化完成
  Task ID: 95ae4bc4-de7b-487a-b4e5-005955aa42b1
  Business: block_unblock_management.logging_enhancer
  Step: logging_enhancer_init
[2025-07-08T16:48:50.918893Z] INFO - 屏蔽管理器初始化完成（简化版-集成批量导入）
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: manager_init
  Details:
    business_type: block_unblock_management
    script_name: integrated_batch_importer
    mode: integrated_batch_import
    components_initialized: ['login_manager', 'token_extractor', 'user_info_extractor', 'notice_client', 'excel_processor', 'stock_client', 'db_query_processor', 'logging_enhancer']
[2025-07-08T16:48:50.919894Z] INFO - 集成批量导入处理器初始化完成（优化版）
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: importer_init
  Details:
    template_config: {'official_url': 'https://salecentersaasapi.yibainetwork.com//template//amazon//import_amazon_stock_adjust_template.xlsx', 'max_file_size': 52428800, 'download_timeout': 60}
    batch_config: {'max_records_per_file': 9999, 'import_interval_min': 3, 'import_interval_max': 5, 'max_concurrent_imports': 1, 'retry_failed_imports': True, 'max_retries': 2}
    auth_config: {'max_auth_retries': 2, 'auth_retry_delay': 3, 'token_check_interval': 300}
    rpa_config: {'enabled': False, 'deprecated': True, 'recommended_alternative': 'API调用模式', 'navigation_timeout': 30000, 'element_timeout': 15000, 'upload_timeout': 30000, 'cross_platform_upload': True, 'retry_on_navigation_failure': True, 'auto_login_on_redirect': True}
    platform: Windows
    platform_version: Windows-10-10.0.19045-SP0
[2025-07-08T16:48:50.919894Z] INFO - 🧹 开始清理集成批量导入临时文件（跨平台）
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    platform: Windows
[2025-07-08T16:48:50.928887Z] INFO - 🧹 临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    success: True
    cleaned_files: 0
    cleaned_dirs: 0
    errors: []
    platform: Windows
    temp_base_dir: C:\Users\<USER>\AppData\Local\Temp
    patterns_checked: ['batch_import_*', 'official_template_*.xlsx', 'integrated_*', '批量导入_*.xlsx', 'template_*_cleaned.xlsx']
[2025-07-08T16:48:50.929888Z] INFO - ✅ 集成批量导入临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    cleaned_files: 0
    cleaned_dirs: 0
[2025-07-08T16:48:50.934886Z] INFO - ✅ 临时文件清理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Details:
    total_cleaned: 0
    temp_dir: C:\Users\<USER>\AppData\Local\Temp
[2025-07-08T16:48:50.934886Z] INFO - ✅ 执行成功: 屏蔽解屏蔽完整流程（集成批量导入）
  Task ID: f12600ed-3f92-4734-a742-1bb8e9b4f843
  Business: block_unblock_management.logging_enhancer
  Step: execution_success
  Details:
    trace_id: trace_1751964364_6185ea03
    step_name: 屏蔽解屏蔽完整流程（集成批量导入）
    execution_time: 166.556s
    context: {'trace_id': 'trace_1751964364_6185ea03', 'step_name': '屏蔽解屏蔽完整流程（集成批量导入）', 'start_time': '2025-07-08T16:46:04.377639', 'end_time': '2025-07-08T16:48:50.934886', 'success': True, 'error_code': None, 'error_message': None, 'performance_metrics': {'execution_time': 166.55625009536743}, 'duration': 166.557247}
[2025-07-08T16:48:50.934886Z] ERROR - 屏蔽解屏蔽任务执行失败: 屏蔽解屏蔽流程执行失败: 转换后的业务数据为空
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.937885Z] DEBUG - 没有找到需要清理的临时文件（Excel处理都在内存中）
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
[2025-07-08T16:48:50.937885Z] INFO - 步骤执行完成: main_execution
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: main_execution_end
  Execution Time: 167.024s
[2025-07-08T16:48:50.937885Z] INFO - 执行异步后置处理
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_post_processing
  Details:
    result_keys: ['success', 'message', 'error']
    result_size: 79
[2025-07-08T16:48:50.937885Z] INFO - 异步后置处理完成
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_post_processing_complete
[2025-07-08T16:48:50.937885Z] INFO - RPA任务执行完成 - 状态: success
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: task_summary
  Details:
    status: success
    total_execution_time: 167.02619099617004
    start_time: 2025-07-08T08:46:03.766456Z
    end_time: 2025-07-08T08:48:50.937885Z
    result: {'success': False, 'message': '屏蔽解屏蔽流程执行失败: 转换后的业务数据为空', 'error': '转换后的业务数据为空'}
    operation_count: 0
    error_count: 0
    retry_count: 0
    step_times: {}
  Execution Time: 167.171s
[2025-07-08T16:48:50.937885Z] INFO - 执行异步最终清理
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: async_final_cleanup
[2025-07-08T16:48:51.301522Z] INFO - Playwright驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: driver_closed
  Details:
    operations: 3
    errors: 0
    screenshots: 0
    execution_time: 167.38582944869995
[2025-07-08T16:48:51.301522Z] INFO - Web驱动已关闭
  Task ID: block_unblock_management_block_unblock_management_20250708_164603_764
  Business: block_unblock_management.block_unblock_management
  Step: web_driver_cleanup
[2025-07-08T16:48:51.301522Z] INFO - 任务执行成功
[2025-07-24T17:25:57.416356Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:32:26.915894Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:32:26.917893Z] INFO - RPA任务开始
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:26.917893Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:26.918894Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:26.918894Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_001
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:32:26.918894Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:32:26.919893Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:26.920893Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:26.920893Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:32:26.921893Z] INFO - 通用Token提取器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:26.921893Z] INFO - 用户信息提取器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:26.922893Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:32:26.923893Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:32:26.924893Z] INFO - RPA任务开始
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.018466Z] INFO - 创建数据库连接池: 127.0.0.1:3306/lingyi
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: create_pool
[2025-07-25T10:32:27.018466Z] INFO - 数据库管理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:32:27.018466Z] INFO - 数据库管理器初始化成功
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.018466Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.019465Z] INFO - RPA任务开始
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.020466Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.020466Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.020466Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_002
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:32:27.021465Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:32:27.021465Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:27.021465Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:27.022466Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:32:27.022466Z] INFO - 通用Token提取器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.023465Z] INFO - 用户信息提取器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.023465Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:32:27.023465Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:32:27.024466Z] INFO - RPA任务开始
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.024466Z] INFO - 数据库管理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:32:27.025465Z] INFO - 数据库管理器初始化成功
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.025465Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.025465Z] DEBUG - 从环境变量获取配置 debugMode: true
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.025465Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.026465Z] DEBUG - 使用默认配置 maxBatchSize: 50
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.026465Z] DEBUG - 从环境变量获取配置 detailConditionList: [{systemSku=*************, creationTime=Sun Dec 31 03:30:00 CST 2023, accountTradeDetailId=510347, sourceOrderNo=FXPK231228500401}, {systemSku=*************, creationTime=Sun Dec 31 02:51:00 CST 2023, accountTradeDetailId=510363, sourceOrderNo=FXPK231230505587}, {systemSku=*************, creationTime=Sun Dec 31 02:51:00 CST 2023, accountTradeDetailId=510364, sourceOrderNo=FXPK231230505768}, {systemSku=*************, creationTime=Sun Dec 31 02:11:00 CST 2023, accountTradeDetailId=510379, sourceOrderNo=FXPK231230512956}, {systemSku=*************, creationTime=Sat Dec 30 17:51:00 CST 2023, accountTradeDetailId=510408, sourceOrderNo=FXPK231226506461}, {systemSku=*************, creationTime=Sat Dec 30 13:22:00 CST 2023, accountTradeDetailId=510410, sourceOrderNo=FXPK231230504901}, {systemSku=*************, creationTime=Sat Dec 30 13:22:00 CST 2023, accountTradeDetailId=510411, sourceOrderNo=FXPK231230504658}, {systemSku=*************, creationTime=Sat Dec 30 12:20:00 CST 2023, accountTradeDetailId=510416, sourceOrderNo=FXPK231230504107}, {systemSku=*************, creationTime=Sat Dec 30 12:20:00 CST 2023, accountTradeDetailId=510417, sourceOrderNo=FXPK231230504124}, {systemSku=*************, creationTime=Sat Dec 30 12:12:00 CST 2023, accountTradeDetailId=510420, sourceOrderNo=FXPK231230503239}, {systemSku=*************, creationTime=Sat Dec 30 10:32:00 CST 2023, accountTradeDetailId=510425, sourceOrderNo=FXPK231230501196}, {systemSku=*************, creationTime=Sat Dec 30 10:31:00 CST 2023, accountTradeDetailId=510426, sourceOrderNo=FXPK231230501477}, {systemSku=TJOT53308, creationTime=Sat Dec 30 08:21:00 CST 2023, accountTradeDetailId=510447, sourceOrderNo=FXPK231229527927}, {systemSku=JY21618, creationTime=Sat Dec 30 08:20:00 CST 2023, accountTradeDetailId=510448, sourceOrderNo=FXPK231229528429}, {systemSku=*************, creationTime=Sat Dec 30 08:18:00 CST 2023, accountTradeDetailId=510449, sourceOrderNo=FXPK231229527764}]
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.026465Z] WARNING - 配置项 detailConditionList 不是有效的JSON格式，使用默认值
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.028465Z] INFO - RPA任务开始
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.028465Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.029466Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.029466Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_003
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:32:27.029466Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:32:27.030466Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:27.030466Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:27.031466Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:32:27.031466Z] INFO - 通用Token提取器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.031466Z] INFO - 用户信息提取器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.031466Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:32:27.032466Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:32:27.032466Z] INFO - RPA任务开始
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.032466Z] INFO - 数据库管理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:32:27.033465Z] INFO - 数据库管理器初始化成功
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.033465Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.033465Z] DEBUG - 从环境变量获取配置 debugMode: true
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.033465Z] INFO - Debug模式：使用内置测试数据
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.034466Z] INFO - RPA任务开始
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.035466Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.035466Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.035466Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_005
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:32:27.035466Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:32:27.036466Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:27.036466Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:32:27.037466Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:32:27.037466Z] INFO - 通用Token提取器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.037466Z] INFO - 用户信息提取器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.038466Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:32:27.038466Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:32:27.038466Z] INFO - RPA任务开始
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:32:27.039465Z] INFO - 数据库管理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:32:27.039465Z] INFO - 数据库管理器初始化成功
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.039465Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.040466Z] DEBUG - 从环境变量获取配置 batchProcessingEnabled: true
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.041022Z] DEBUG - 任务 1 验证通过
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.041022Z] WARNING - 任务 2 验证失败: creationTime格式错误: invalid_time
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.041022Z] DEBUG - 任务添加到失败列表: test_002 - creationTime格式错误: invalid_time
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.041022Z] WARNING - 任务 3 验证失败: 缺少必需字段: systemSku, accountTradeDetailId
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.041992Z] DEBUG - 任务添加到失败列表: unknown - 缺少必需字段: systemSku, accountTradeDetailId
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:32:27.041992Z] INFO - 批量任务验证完成，有效任务: 1, 无效任务: 2
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.457345Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:33:33.459345Z] INFO - RPA任务开始
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.459345Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.459345Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.460344Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_001
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:33:33.461345Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:33:33.462345Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.462345Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.463345Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:33:33.463345Z] INFO - 通用Token提取器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.463345Z] INFO - 用户信息提取器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.464346Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:33:33.464346Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:33:33.465345Z] INFO - RPA任务开始
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.515346Z] INFO - 创建数据库连接池: 127.0.0.1:3306/lingyi
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: create_pool
[2025-07-25T10:33:33.515346Z] INFO - 数据库管理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:33:33.515346Z] INFO - 数据库管理器初始化成功
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.515346Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.516345Z] INFO - RPA任务开始
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.517345Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.517345Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.517345Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_002
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:33:33.518345Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:33:33.518345Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.518345Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.519345Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:33:33.520345Z] INFO - 通用Token提取器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.520345Z] INFO - 用户信息提取器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.520345Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:33:33.521346Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:33:33.521346Z] INFO - RPA任务开始
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.522346Z] INFO - 数据库管理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:33:33.523346Z] INFO - 数据库管理器初始化成功
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.524347Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.524347Z] DEBUG - 从环境变量获取配置 debugMode: true
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.524347Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.524347Z] DEBUG - 使用默认配置 maxBatchSize: 50
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.524347Z] DEBUG - 从环境变量获取配置 detailConditionList: [{systemSku=*************, creationTime=Sun Dec 31 03:30:00 CST 2023, accountTradeDetailId=510347, sourceOrderNo=FXPK231228500401}, {systemSku=*************, creationTime=Sun Dec 31 02:51:00 CST 2023, accountTradeDetailId=510363, sourceOrderNo=FXPK231230505587}, {systemSku=*************, creationTime=Sun Dec 31 02:51:00 CST 2023, accountTradeDetailId=510364, sourceOrderNo=FXPK231230505768}, {systemSku=*************, creationTime=Sun Dec 31 02:11:00 CST 2023, accountTradeDetailId=510379, sourceOrderNo=FXPK231230512956}, {systemSku=*************, creationTime=Sat Dec 30 17:51:00 CST 2023, accountTradeDetailId=510408, sourceOrderNo=FXPK231226506461}, {systemSku=*************, creationTime=Sat Dec 30 13:22:00 CST 2023, accountTradeDetailId=510410, sourceOrderNo=FXPK231230504901}, {systemSku=*************, creationTime=Sat Dec 30 13:22:00 CST 2023, accountTradeDetailId=510411, sourceOrderNo=FXPK231230504658}, {systemSku=*************, creationTime=Sat Dec 30 12:20:00 CST 2023, accountTradeDetailId=510416, sourceOrderNo=FXPK231230504107}, {systemSku=*************, creationTime=Sat Dec 30 12:20:00 CST 2023, accountTradeDetailId=510417, sourceOrderNo=FXPK231230504124}, {systemSku=*************, creationTime=Sat Dec 30 12:12:00 CST 2023, accountTradeDetailId=510420, sourceOrderNo=FXPK231230503239}, {systemSku=*************, creationTime=Sat Dec 30 10:32:00 CST 2023, accountTradeDetailId=510425, sourceOrderNo=FXPK231230501196}, {systemSku=*************, creationTime=Sat Dec 30 10:31:00 CST 2023, accountTradeDetailId=510426, sourceOrderNo=FXPK231230501477}, {systemSku=TJOT53308, creationTime=Sat Dec 30 08:21:00 CST 2023, accountTradeDetailId=510447, sourceOrderNo=FXPK231229527927}, {systemSku=JY21618, creationTime=Sat Dec 30 08:20:00 CST 2023, accountTradeDetailId=510448, sourceOrderNo=FXPK231229528429}, {systemSku=*************, creationTime=Sat Dec 30 08:18:00 CST 2023, accountTradeDetailId=510449, sourceOrderNo=FXPK231229527764}]
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.524347Z] WARNING - 配置项 detailConditionList 不是有效的JSON格式，使用默认值
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.526345Z] INFO - RPA任务开始
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.526345Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.527345Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.527345Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_003
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:33:33.527345Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:33:33.528346Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.528346Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.528346Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:33:33.529345Z] INFO - 通用Token提取器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.529345Z] INFO - 用户信息提取器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.529345Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:33:33.530345Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:33:33.530345Z] INFO - RPA任务开始
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.531345Z] INFO - 数据库管理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:33:33.531345Z] INFO - 数据库管理器初始化成功
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.531345Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.531345Z] DEBUG - 从环境变量获取配置 debugMode: true
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.532345Z] INFO - Debug模式：使用内置测试数据
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.533345Z] INFO - RPA任务开始
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.534345Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.534345Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.534345Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_005
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:33:33.534345Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:33:33.535345Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.535345Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:33:33.535345Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:33:33.536345Z] INFO - 通用Token提取器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.536345Z] INFO - 用户信息提取器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.536345Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:33:33.536345Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:33:33.537345Z] INFO - RPA任务开始
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:33:33.537345Z] INFO - 数据库管理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:33:33.537345Z] INFO - 数据库管理器初始化成功
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.537345Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.538346Z] DEBUG - 从环境变量获取配置 batchProcessingEnabled: true
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.538346Z] DEBUG - 任务 1 验证通过
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.538346Z] WARNING - 任务 2 验证失败: creationTime格式错误: invalid_time
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.538346Z] DEBUG - 任务添加到失败列表: test_002 - creationTime格式错误: invalid_time
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.539346Z] WARNING - 任务 3 验证失败: 缺少必需字段: systemSku, accountTradeDetailId
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.539346Z] DEBUG - 任务添加到失败列表: unknown - 缺少必需字段: systemSku, accountTradeDetailId
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:33:33.539346Z] INFO - 批量任务验证完成，有效任务: 1, 无效任务: 2
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.385207Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:34:41.387207Z] INFO - RPA任务开始
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.387207Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.387207Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.388207Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_001
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:34:41.388207Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:34:41.388207Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.388207Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.389207Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:34:41.389207Z] INFO - 通用Token提取器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.389207Z] INFO - 用户信息提取器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.390207Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:34:41.390207Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:34:41.391207Z] INFO - RPA任务开始
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.488870Z] INFO - 创建数据库连接池: 127.0.0.1:3306/lingyi
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: create_pool
[2025-07-25T10:34:41.488870Z] INFO - 数据库管理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:34:41.489870Z] INFO - 数据库管理器初始化成功
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.489870Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_001
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.491870Z] INFO - RPA任务开始
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.491870Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.492869Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.492869Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_002
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:34:41.492869Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:34:41.493870Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.493870Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.494869Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:34:41.494869Z] INFO - 通用Token提取器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.494869Z] INFO - 用户信息提取器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.495870Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:34:41.495870Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:34:41.496869Z] INFO - RPA任务开始
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.496869Z] INFO - 数据库管理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:34:41.497869Z] INFO - 数据库管理器初始化成功
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.497869Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.497869Z] DEBUG - 从环境变量获取配置 debugMode: true
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.498869Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.498869Z] DEBUG - 使用默认配置 maxBatchSize: 50
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.499870Z] DEBUG - 从环境变量获取配置 detailConditionList: [{systemSku=*************, creationTime=Sun Dec 31 03:30:00 CST 2023, accountTradeDetailId=510347, sourceOrderNo=FXPK231228500401}, {systemSku=*************, creationTime=Sun Dec 31 02:51:00 CST 2023, accountTradeDetailId=510363, sourceOrderNo=FXPK231230505587}, {systemSku=*************, creationTime=Sun Dec 31 02:51:00 CST 2023, accountTradeDetailId=510364, sourceOrderNo=FXPK231230505768}, {systemSku=*************, creationTime=Sun Dec 31 02:11:00 CST 2023, accountTradeDetailId=510379, sourceOrderNo=FXPK231230512956}, {systemSku=*************, creationTime=Sat Dec 30 17:51:00 CST 2023, accountTradeDetailId=510408, sourceOrderNo=FXPK231226506461}, {systemSku=*************, creationTime=Sat Dec 30 13:22:00 CST 2023, accountTradeDetailId=510410, sourceOrderNo=FXPK231230504901}, {systemSku=*************, creationTime=Sat Dec 30 13:22:00 CST 2023, accountTradeDetailId=510411, sourceOrderNo=FXPK231230504658}, {systemSku=*************, creationTime=Sat Dec 30 12:20:00 CST 2023, accountTradeDetailId=510416, sourceOrderNo=FXPK231230504107}, {systemSku=*************, creationTime=Sat Dec 30 12:20:00 CST 2023, accountTradeDetailId=510417, sourceOrderNo=FXPK231230504124}, {systemSku=*************, creationTime=Sat Dec 30 12:12:00 CST 2023, accountTradeDetailId=510420, sourceOrderNo=FXPK231230503239}, {systemSku=*************, creationTime=Sat Dec 30 10:32:00 CST 2023, accountTradeDetailId=510425, sourceOrderNo=FXPK231230501196}, {systemSku=*************, creationTime=Sat Dec 30 10:31:00 CST 2023, accountTradeDetailId=510426, sourceOrderNo=FXPK231230501477}, {systemSku=TJOT53308, creationTime=Sat Dec 30 08:21:00 CST 2023, accountTradeDetailId=510447, sourceOrderNo=FXPK231229527927}, {systemSku=JY21618, creationTime=Sat Dec 30 08:20:00 CST 2023, accountTradeDetailId=510448, sourceOrderNo=FXPK231229528429}, {systemSku=*************, creationTime=Sat Dec 30 08:18:00 CST 2023, accountTradeDetailId=510449, sourceOrderNo=FXPK231229527764}]
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.499870Z] WARNING - 配置项 detailConditionList 不是有效的JSON格式，使用默认值
  Task ID: test_002
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.500870Z] INFO - RPA任务开始
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.501869Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.501869Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.502871Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_003
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:34:41.502871Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:34:41.503869Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.503869Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.504868Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:34:41.504868Z] INFO - 通用Token提取器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.505868Z] INFO - 用户信息提取器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.505868Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:34:41.505868Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:34:41.506868Z] INFO - RPA任务开始
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.507870Z] INFO - 数据库管理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:34:41.507870Z] INFO - 数据库管理器初始化成功
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.507870Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.508870Z] DEBUG - 从环境变量获取配置 debugMode: true
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.508870Z] INFO - Debug模式：使用内置测试数据
  Task ID: test_003
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.508870Z] INFO - RPA任务开始
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Step: task_start
[2025-07-25T10:34:41.508870Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:34:41.511869Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Step: config_load
[2025-07-25T10:34:41.511869Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Step: config_load
[2025-07-25T10:34:41.512870Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:34:41.512870Z] INFO - 通用Token提取器初始化完成
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
[2025-07-25T10:34:41.512870Z] INFO - 用户信息提取器初始化完成
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
[2025-07-25T10:34:41.512870Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Details:
    business_type: shop_account_info
    script_name: test
[2025-07-25T10:34:41.513868Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: 87a6f47a-4932-4e42-9ce3-9b0e4cb3d0db
  Business: shop_account_info.test_api_client
  Details:
    business_type: shop_account_info
    script_name: test
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:34:41.514868Z] INFO - RPA任务开始
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.514868Z] DEBUG - 获取业务配置异常: ConfigManager() takes no arguments
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.515868Z] DEBUG - 使用默认配置 WEB_DRIVER_TYPE: playwright
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.515868Z] INFO - 异步RPA脚本初始化完成: shop_account_info.shop_account_processor_api
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: async_rpa_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    task_id: test_005
    config_keys: ['business_type', 'script_name', 'task_params', 'timeout', 'retry_count', '_comments', '_comment_business_config', 'timeWindowHours', '_timeWindowHours_comment', '_comment_batch_config', 'debugMode', '_debugMode_comment', 'batchProcessingEnabled', '_batchProcessingEnabled_comment', 'maxBatchSize', '_maxBatchSize_comment', '_comment_override_global', 'PLAYWRIGHT_PAGE_LOAD_WAIT', '_PLAYWRIGHT_PAGE_LOAD_WAIT_comment', 'PLAYWRIGHT_TIMEOUT', '_PLAYWRIGHT_TIMEOUT_comment', 'LOG_LEVEL', '_LOG_LEVEL_comment', 'PLAYWRIGHT_HEADLESS', 'PLAYWRIGHT_WINDOW_SIZE', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD']
    async_mode: True
    driver_type: playwright
[2025-07-25T10:34:41.515868Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T10:34:41.516869Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.516869Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: config_load
[2025-07-25T10:34:41.517870Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T10:34:41.517870Z] INFO - 通用Token提取器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.517870Z] INFO - 用户信息提取器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.518871Z] INFO - ✅ 用户信息网络提取工具初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
[2025-07-25T10:34:41.518871Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Details:
    business_type: shop_account_info
    script_name: shop_account_processor_api
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T10:34:41.518871Z] INFO - RPA任务开始
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: task_start
[2025-07-25T10:34:41.519871Z] INFO - 数据库管理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
  Step: db_init
  Details:
    host: 127.0.0.1
    database: lingyi
    pool_key: 127.0.0.1:3306/lingyi
[2025-07-25T10:34:41.519871Z] INFO - 数据库管理器初始化成功
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.519871Z] INFO - ✅ 店铺账户信息API处理器初始化完成
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.519871Z] DEBUG - 从环境变量获取配置 batchProcessingEnabled: true
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.520870Z] DEBUG - 任务 1 验证通过
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.520870Z] WARNING - 任务 2 验证失败: creationTime格式错误: invalid_time
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.520870Z] DEBUG - 任务添加到失败列表: test_002 - creationTime格式错误: invalid_time
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.520870Z] WARNING - 任务 3 验证失败: 缺少必需字段: systemSku, accountTradeDetailId
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.520870Z] DEBUG - 任务添加到失败列表: unknown - 缺少必需字段: systemSku, accountTradeDetailId
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:34:41.521871Z] INFO - 批量任务验证完成，有效任务: 1, 无效任务: 2
  Task ID: test_005
  Business: shop_account_info.shop_account_processor_api
[2025-07-25T10:54:43.072682Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:57:37.668699Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:57:37.669698Z] INFO - ✅ 订单详情缓存初始化完成 - 缓存时长: 1分钟, 最大大小: 100
[2025-07-25T10:57:37.669698Z] DEBUG - 💾 缓存已设置: order_001 - 键: 7a97343cb879cf68a989d435fc9692b3, 缓存大小: 1
[2025-07-25T10:57:37.669698Z] DEBUG - 💾 缓存已设置: order_002 - 键: 006f2a128e5ef964fed63bdee98d67de, 缓存大小: 2
[2025-07-25T10:57:37.670700Z] DEBUG - 💾 缓存已设置: order_003 - 键: 9f7fd5e1c53f60839dfd74cbfdf85388, 缓存大小: 3
[2025-07-25T10:57:37.670700Z] DEBUG - ✅ 缓存命中: order_001 - 键: 7a97343cb879cf68a989d435fc9692b3, 年龄: 0.0秒
[2025-07-25T10:57:37.670700Z] DEBUG - ✅ 缓存命中: order_002 - 键: 006f2a128e5ef964fed63bdee98d67de, 年龄: 0.0秒
[2025-07-25T10:57:37.671698Z] DEBUG - ✅ 缓存命中: order_003 - 键: 9f7fd5e1c53f60839dfd74cbfdf85388, 年龄: 0.0秒
[2025-07-25T10:57:42.677584Z] INFO - ✅ 并发处理器初始化完成 - 最大并发: 2, 请求间隔: 0.5秒
[2025-07-25T10:57:42.677584Z] INFO - 🚀 开始并发处理订单 - 总数: 5, 最大并发: 2, 请求间隔: 0.5秒
[2025-07-25T10:57:42.677584Z] DEBUG - 🔄 开始处理订单: order_001
[2025-07-25T10:57:42.678584Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:57:42.882313Z] DEBUG - ✅ 订单处理成功: order_001 - 耗时: 0.20s, 索引: 0
[2025-07-25T10:57:43.182349Z] DEBUG - 🔄 开始处理订单: order_002
[2025-07-25T10:57:43.182871Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:57:43.384753Z] DEBUG - ✅ 订单处理成功: order_002 - 耗时: 0.20s, 索引: 1
[2025-07-25T10:57:43.696490Z] DEBUG - 🔄 开始处理订单: order_003_fail
[2025-07-25T10:57:43.697492Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:57:43.898567Z] WARNING - ❌ 订单处理失败: order_003_fail - 错误: 模拟API调用失败: order_003_fail, 索引: 2, 异常类型: Exception
[2025-07-25T10:57:44.198211Z] DEBUG - 🔄 开始处理订单: order_004
[2025-07-25T10:57:44.199210Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:57:44.401478Z] DEBUG - ✅ 订单处理成功: order_004 - 耗时: 0.20s, 索引: 3
[2025-07-25T10:57:44.699213Z] DEBUG - 🔄 开始处理订单: order_005
[2025-07-25T10:57:44.902930Z] DEBUG - ✅ 订单处理成功: order_005 - 耗时: 0.20s, 索引: 4
[2025-07-25T10:57:44.903465Z] INFO - 🎯 并发处理完成 - 总订单数: 5, 成功数: 4, 失败数: 1, 成功率: 80.0%, 总耗时: 2.23秒, 平均耗时: 0.45秒/订单, 异常类型: {'Exception': 1}
[2025-07-25T10:57:44.904523Z] INFO - ✅ 并发处理器初始化完成 - 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:57:44.904523Z] INFO - ✅ 批处理器初始化完成 - 批大小: 3, 批间隔: 0.5秒
[2025-07-25T10:57:44.905047Z] INFO - 📦 开始分批处理 - 总项目: 10, 批大小: 3, 总批数: 4
[2025-07-25T10:57:44.905578Z] INFO - 🔄 处理批次 1/4 - 项目数: 3, 范围: 0-2
[2025-07-25T10:57:44.906084Z] INFO - 🚀 开始并发处理订单 - 总数: 3, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:57:44.906084Z] DEBUG - 🔄 开始处理订单: batch_order_000
[2025-07-25T10:57:44.907095Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:57:45.013348Z] DEBUG - ✅ 订单处理成功: batch_order_000 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:57:45.230929Z] DEBUG - 🔄 开始处理订单: batch_order_001
[2025-07-25T10:57:45.231468Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:57:45.341470Z] DEBUG - ✅ 订单处理成功: batch_order_001 - 耗时: 0.11s, 索引: 1
[2025-07-25T10:57:45.546613Z] DEBUG - 🔄 开始处理订单: batch_order_002
[2025-07-25T10:57:45.657215Z] DEBUG - ✅ 订单处理成功: batch_order_002 - 耗时: 0.11s, 索引: 2
[2025-07-25T10:57:45.657536Z] INFO - 🎯 并发处理完成 - 总订单数: 3, 成功数: 3, 失败数: 0, 成功率: 100.0%, 总耗时: 0.75秒, 平均耗时: 0.25秒/订单, 异常类型: 无异常
[2025-07-25T10:57:45.658046Z] DEBUG - ⏱️ 批次间隔等待: 0.5秒
[2025-07-25T10:57:46.160689Z] INFO - 🔄 处理批次 2/4 - 项目数: 3, 范围: 3-5
[2025-07-25T10:57:46.161690Z] INFO - 🚀 开始并发处理订单 - 总数: 3, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:57:46.162687Z] DEBUG - 🔄 开始处理订单: batch_order_003
[2025-07-25T10:57:46.163688Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:57:46.269614Z] DEBUG - ✅ 订单处理成功: batch_order_003 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:57:46.473863Z] DEBUG - 🔄 开始处理订单: batch_order_004
[2025-07-25T10:57:46.474863Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:57:46.601068Z] DEBUG - ✅ 订单处理成功: batch_order_004 - 耗时: 0.12s, 索引: 1
[2025-07-25T10:57:46.791816Z] DEBUG - 🔄 开始处理订单: batch_order_005
[2025-07-25T10:57:46.896880Z] DEBUG - ✅ 订单处理成功: batch_order_005 - 耗时: 0.10s, 索引: 2
[2025-07-25T10:57:46.903883Z] INFO - 🎯 并发处理完成 - 总订单数: 3, 成功数: 6, 失败数: 0, 成功率: 200.0%, 总耗时: 0.74秒, 平均耗时: 0.25秒/订单, 异常类型: 无异常
[2025-07-25T10:57:46.906882Z] DEBUG - ⏱️ 批次间隔等待: 0.5秒
[2025-07-25T10:57:47.413480Z] INFO - 🔄 处理批次 3/4 - 项目数: 3, 范围: 6-8
[2025-07-25T10:57:47.414006Z] INFO - 🚀 开始并发处理订单 - 总数: 3, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:57:47.414006Z] DEBUG - 🔄 开始处理订单: batch_order_006
[2025-07-25T10:57:47.414536Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:57:47.522369Z] DEBUG - ✅ 订单处理成功: batch_order_006 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:57:47.728846Z] DEBUG - 🔄 开始处理订单: batch_order_007
[2025-07-25T10:57:47.728846Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:57:47.838491Z] DEBUG - ✅ 订单处理成功: batch_order_007 - 耗时: 0.11s, 索引: 1
[2025-07-25T10:57:48.040890Z] DEBUG - 🔄 开始处理订单: batch_order_008
[2025-07-25T10:57:48.151327Z] DEBUG - ✅ 订单处理成功: batch_order_008 - 耗时: 0.11s, 索引: 2
[2025-07-25T10:57:48.151327Z] INFO - 🎯 并发处理完成 - 总订单数: 3, 成功数: 9, 失败数: 0, 成功率: 300.0%, 总耗时: 0.74秒, 平均耗时: 0.25秒/订单, 异常类型: 无异常
[2025-07-25T10:57:48.152340Z] DEBUG - ⏱️ 批次间隔等待: 0.5秒
[2025-07-25T10:57:48.666260Z] INFO - 🔄 处理批次 4/4 - 项目数: 1, 范围: 9-9
[2025-07-25T10:57:48.666260Z] INFO - 🚀 开始并发处理订单 - 总数: 1, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:57:48.666260Z] DEBUG - 🔄 开始处理订单: batch_order_009
[2025-07-25T10:57:48.775072Z] DEBUG - ✅ 订单处理成功: batch_order_009 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:57:48.775072Z] INFO - 🎯 并发处理完成 - 总订单数: 1, 成功数: 10, 失败数: 0, 成功率: 1000.0%, 总耗时: 0.11秒, 平均耗时: 0.11秒/订单, 异常类型: 无异常
[2025-07-25T10:57:48.776067Z] INFO - 🎯 分批处理完成 - 总批数: 4, 总结果: 10
[2025-07-25T10:57:48.776067Z] INFO - ✅ 订单详情缓存初始化完成 - 缓存时长: 0.1分钟, 最大大小: 50
[2025-07-25T10:57:48.776067Z] INFO - ✅ 缓存管理器初始化完成 - 自动清理间隔: 3秒
[2025-07-25T10:57:48.777067Z] DEBUG - 💾 缓存已设置: mgr_order_001 - 键: 772a85e419bf24e62f7e2265c3ccbee7, 缓存大小: 1
[2025-07-25T10:57:48.777067Z] DEBUG - 💾 缓存已设置: mgr_order_002 - 键: e1f3d95ab0eb48643f05cd5ef634675f, 缓存大小: 2
[2025-07-25T10:57:48.777067Z] DEBUG - 💾 缓存已设置: mgr_order_003 - 键: 4187c36908c069cd5055ed81a5fd1725, 缓存大小: 3
[2025-07-25T10:57:52.785392Z] DEBUG - ✅ 缓存命中: mgr_order_001 - 键: 772a85e419bf24e62f7e2265c3ccbee7, 年龄: 4.0秒
[2025-07-25T10:57:54.428728Z] INFO - ✅ 并发处理器初始化完成 - 最大并发: 3, 请求间隔: 0.1秒
[2025-07-25T10:57:54.429723Z] INFO - 🚀 开始并发处理订单 - 总数: 8, 最大并发: 3, 请求间隔: 0.1秒
[2025-07-25T10:57:54.430724Z] DEBUG - 🔄 开始处理订单: perf_order_000
[2025-07-25T10:57:54.430724Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:54.539748Z] DEBUG - 🔄 开始处理订单: perf_order_001
[2025-07-25T10:57:54.540744Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:54.633718Z] DEBUG - ✅ 订单处理成功: perf_order_000 - 耗时: 0.20s, 索引: 0
[2025-07-25T10:57:54.634715Z] DEBUG - 🔄 开始处理订单: perf_order_002
[2025-07-25T10:57:54.634715Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:54.745791Z] DEBUG - 🔄 开始处理订单: perf_order_003
[2025-07-25T10:57:54.745791Z] DEBUG - ✅ 订单处理成功: perf_order_001 - 耗时: 0.21s, 索引: 1
[2025-07-25T10:57:54.746795Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:54.841100Z] DEBUG - ✅ 订单处理成功: perf_order_002 - 耗时: 0.21s, 索引: 2
[2025-07-25T10:57:54.842086Z] DEBUG - 🔄 开始处理订单: perf_order_004
[2025-07-25T10:57:54.842086Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:54.950451Z] DEBUG - 🔄 开始处理订单: perf_order_005
[2025-07-25T10:57:54.950451Z] DEBUG - ✅ 订单处理成功: perf_order_003 - 耗时: 0.20s, 索引: 3
[2025-07-25T10:57:54.950451Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:55.045083Z] DEBUG - ✅ 订单处理成功: perf_order_004 - 耗时: 0.20s, 索引: 4
[2025-07-25T10:57:55.045083Z] DEBUG - 🔄 开始处理订单: perf_order_006
[2025-07-25T10:57:55.046079Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:57:55.139051Z] DEBUG - ✅ 订单处理成功: perf_order_005 - 耗时: 0.19s, 索引: 5
[2025-07-25T10:57:55.170458Z] DEBUG - 🔄 开始处理订单: perf_order_007
[2025-07-25T10:57:55.248834Z] DEBUG - ✅ 订单处理成功: perf_order_006 - 耗时: 0.20s, 索引: 6
[2025-07-25T10:57:55.377109Z] DEBUG - ✅ 订单处理成功: perf_order_007 - 耗时: 0.21s, 索引: 7
[2025-07-25T10:57:55.377109Z] INFO - 🎯 并发处理完成 - 总订单数: 8, 成功数: 8, 失败数: 0, 成功率: 100.0%, 总耗时: 0.95秒, 平均耗时: 0.12秒/订单, 异常类型: 无异常
[2025-07-25T10:58:28.227522Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T10:58:28.229173Z] INFO - ✅ 订单详情缓存初始化完成 - 缓存时长: 1分钟, 最大大小: 100
[2025-07-25T10:58:28.229704Z] DEBUG - 💾 缓存已设置: order_001 - 键: 7a97343cb879cf68a989d435fc9692b3, 缓存大小: 1
[2025-07-25T10:58:28.229704Z] DEBUG - 💾 缓存已设置: order_002 - 键: 006f2a128e5ef964fed63bdee98d67de, 缓存大小: 2
[2025-07-25T10:58:28.229704Z] DEBUG - 💾 缓存已设置: order_003 - 键: 9f7fd5e1c53f60839dfd74cbfdf85388, 缓存大小: 3
[2025-07-25T10:58:28.230232Z] DEBUG - ✅ 缓存命中: order_001 - 键: 7a97343cb879cf68a989d435fc9692b3, 年龄: 0.0秒
[2025-07-25T10:58:28.230232Z] DEBUG - ✅ 缓存命中: order_002 - 键: 006f2a128e5ef964fed63bdee98d67de, 年龄: 0.0秒
[2025-07-25T10:58:28.230761Z] DEBUG - ✅ 缓存命中: order_003 - 键: 9f7fd5e1c53f60839dfd74cbfdf85388, 年龄: 0.0秒
[2025-07-25T10:58:35.244814Z] DEBUG - ⏰ 缓存过期已删除: order_001
[2025-07-25T10:58:35.244814Z] DEBUG - ❌ 缓存未命中: order_001
[2025-07-25T10:58:35.245815Z] INFO - ✅ 并发处理器初始化完成 - 最大并发: 2, 请求间隔: 0.5秒
[2025-07-25T10:58:35.245815Z] INFO - 🚀 开始并发处理订单 - 总数: 5, 最大并发: 2, 请求间隔: 0.5秒
[2025-07-25T10:58:35.245815Z] DEBUG - 🔄 开始处理订单: order_001
[2025-07-25T10:58:35.245815Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:58:35.449223Z] DEBUG - ✅ 订单处理成功: order_001 - 耗时: 0.20s, 索引: 0
[2025-07-25T10:58:35.748873Z] DEBUG - 🔄 开始处理订单: order_002
[2025-07-25T10:58:35.749875Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:58:35.940612Z] DEBUG - ✅ 订单处理成功: order_002 - 耗时: 0.19s, 索引: 1
[2025-07-25T10:58:36.268001Z] DEBUG - 🔄 开始处理订单: order_003_fail
[2025-07-25T10:58:36.269907Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:58:36.487231Z] WARNING - ❌ 订单处理失败: order_003_fail - 错误: 模拟API调用失败: order_003_fail, 索引: 2, 异常类型: Exception
[2025-07-25T10:58:36.769153Z] DEBUG - 🔄 开始处理订单: order_004
[2025-07-25T10:58:36.770151Z] DEBUG - ⏱️ 频率限制等待: 0.50秒
[2025-07-25T10:58:36.972426Z] DEBUG - ✅ 订单处理成功: order_004 - 耗时: 0.20s, 索引: 3
[2025-07-25T10:58:37.275443Z] DEBUG - 🔄 开始处理订单: order_005
[2025-07-25T10:58:37.480592Z] DEBUG - ✅ 订单处理成功: order_005 - 耗时: 0.21s, 索引: 4
[2025-07-25T10:58:37.482595Z] INFO - 🎯 并发处理完成 - 总订单数: 5, 成功数: 4, 失败数: 1, 成功率: 80.0%, 总耗时: 2.24秒, 平均耗时: 0.45秒/订单, 异常类型: {'Exception': 1}
[2025-07-25T10:58:37.483595Z] INFO - ✅ 并发处理器初始化完成 - 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:58:37.483595Z] INFO - ✅ 批处理器初始化完成 - 批大小: 3, 批间隔: 0.5秒
[2025-07-25T10:58:37.484595Z] INFO - 📦 开始分批处理 - 总项目: 10, 批大小: 3, 总批数: 4
[2025-07-25T10:58:37.484595Z] INFO - 🔄 处理批次 1/4 - 项目数: 3, 范围: 0-2
[2025-07-25T10:58:37.485595Z] INFO - 🚀 开始并发处理订单 - 总数: 3, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:58:37.485595Z] DEBUG - 🔄 开始处理订单: batch_order_000
[2025-07-25T10:58:37.486596Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:58:37.588022Z] DEBUG - ✅ 订单处理成功: batch_order_000 - 耗时: 0.10s, 索引: 0
[2025-07-25T10:58:37.808373Z] DEBUG - 🔄 开始处理订单: batch_order_001
[2025-07-25T10:58:37.809374Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:58:37.918647Z] DEBUG - ✅ 订单处理成功: batch_order_001 - 耗时: 0.11s, 索引: 1
[2025-07-25T10:58:38.123799Z] DEBUG - 🔄 开始处理订单: batch_order_002
[2025-07-25T10:58:38.232598Z] DEBUG - ✅ 订单处理成功: batch_order_002 - 耗时: 0.11s, 索引: 2
[2025-07-25T10:58:38.232598Z] INFO - 🎯 并发处理完成 - 总订单数: 3, 成功数: 3, 失败数: 0, 成功率: 100.0%, 总耗时: 0.75秒, 平均耗时: 0.25秒/订单, 异常类型: 无异常
[2025-07-25T10:58:38.233607Z] DEBUG - ⏱️ 批次间隔等待: 0.5秒
[2025-07-25T10:58:38.735843Z] INFO - 🔄 处理批次 2/4 - 项目数: 3, 范围: 3-5
[2025-07-25T10:58:38.735843Z] INFO - 🚀 开始并发处理订单 - 总数: 3, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:58:38.736855Z] DEBUG - 🔄 开始处理订单: batch_order_003
[2025-07-25T10:58:38.736855Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:58:38.842612Z] DEBUG - ✅ 订单处理成功: batch_order_003 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:58:39.046918Z] DEBUG - 🔄 开始处理订单: batch_order_004
[2025-07-25T10:58:39.047920Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:58:39.157837Z] DEBUG - ✅ 订单处理成功: batch_order_004 - 耗时: 0.11s, 索引: 1
[2025-07-25T10:58:39.364288Z] DEBUG - 🔄 开始处理订单: batch_order_005
[2025-07-25T10:58:39.474497Z] DEBUG - ✅ 订单处理成功: batch_order_005 - 耗时: 0.11s, 索引: 2
[2025-07-25T10:58:39.475497Z] INFO - 🎯 并发处理完成 - 总订单数: 3, 成功数: 6, 失败数: 0, 成功率: 200.0%, 总耗时: 0.74秒, 平均耗时: 0.25秒/订单, 异常类型: 无异常
[2025-07-25T10:58:39.475497Z] DEBUG - ⏱️ 批次间隔等待: 0.5秒
[2025-07-25T10:58:39.979319Z] INFO - 🔄 处理批次 3/4 - 项目数: 3, 范围: 6-8
[2025-07-25T10:58:39.980320Z] INFO - 🚀 开始并发处理订单 - 总数: 3, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:58:39.980320Z] DEBUG - 🔄 开始处理订单: batch_order_006
[2025-07-25T10:58:39.980320Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:58:40.090572Z] DEBUG - ✅ 订单处理成功: batch_order_006 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:58:40.294796Z] DEBUG - 🔄 开始处理订单: batch_order_007
[2025-07-25T10:58:40.295800Z] DEBUG - ⏱️ 频率限制等待: 0.30秒
[2025-07-25T10:58:40.404506Z] DEBUG - ✅ 订单处理成功: batch_order_007 - 耗时: 0.11s, 索引: 1
[2025-07-25T10:58:40.609812Z] DEBUG - 🔄 开始处理订单: batch_order_008
[2025-07-25T10:58:40.720122Z] DEBUG - ✅ 订单处理成功: batch_order_008 - 耗时: 0.11s, 索引: 2
[2025-07-25T10:58:40.721123Z] INFO - 🎯 并发处理完成 - 总订单数: 3, 成功数: 9, 失败数: 0, 成功率: 300.0%, 总耗时: 0.74秒, 平均耗时: 0.25秒/订单, 异常类型: 无异常
[2025-07-25T10:58:40.721123Z] DEBUG - ⏱️ 批次间隔等待: 0.5秒
[2025-07-25T10:58:41.235606Z] INFO - 🔄 处理批次 4/4 - 项目数: 1, 范围: 9-9
[2025-07-25T10:58:41.235606Z] INFO - 🚀 开始并发处理订单 - 总数: 1, 最大并发: 2, 请求间隔: 0.3秒
[2025-07-25T10:58:41.236615Z] DEBUG - 🔄 开始处理订单: batch_order_009
[2025-07-25T10:58:41.345124Z] DEBUG - ✅ 订单处理成功: batch_order_009 - 耗时: 0.11s, 索引: 0
[2025-07-25T10:58:41.346126Z] INFO - 🎯 并发处理完成 - 总订单数: 1, 成功数: 10, 失败数: 0, 成功率: 1000.0%, 总耗时: 0.11秒, 平均耗时: 0.11秒/订单, 异常类型: 无异常
[2025-07-25T10:58:41.346126Z] INFO - 🎯 分批处理完成 - 总批数: 4, 总结果: 10
[2025-07-25T10:58:41.347127Z] INFO - ✅ 订单详情缓存初始化完成 - 缓存时长: 0.1分钟, 最大大小: 50
[2025-07-25T10:58:41.347127Z] INFO - ✅ 缓存管理器初始化完成 - 自动清理间隔: 3秒
[2025-07-25T10:58:41.348127Z] DEBUG - 💾 缓存已设置: mgr_order_001 - 键: 772a85e419bf24e62f7e2265c3ccbee7, 缓存大小: 1
[2025-07-25T10:58:41.348127Z] DEBUG - 💾 缓存已设置: mgr_order_002 - 键: e1f3d95ab0eb48643f05cd5ef634675f, 缓存大小: 2
[2025-07-25T10:58:41.348127Z] DEBUG - 💾 缓存已设置: mgr_order_003 - 键: 4187c36908c069cd5055ed81a5fd1725, 缓存大小: 3
[2025-07-25T10:58:45.537645Z] DEBUG - ✅ 缓存命中: mgr_order_001 - 键: 772a85e419bf24e62f7e2265c3ccbee7, 年龄: 4.2秒
[2025-07-25T10:58:47.311265Z] INFO - ✅ 并发处理器初始化完成 - 最大并发: 3, 请求间隔: 0.1秒
[2025-07-25T10:58:47.311265Z] INFO - 🚀 开始并发处理订单 - 总数: 8, 最大并发: 3, 请求间隔: 0.1秒
[2025-07-25T10:58:47.312276Z] DEBUG - 🔄 开始处理订单: perf_order_000
[2025-07-25T10:58:47.312276Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:47.421377Z] DEBUG - 🔄 开始处理订单: perf_order_001
[2025-07-25T10:58:47.421911Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:47.515599Z] DEBUG - ✅ 订单处理成功: perf_order_000 - 耗时: 0.20s, 索引: 0
[2025-07-25T10:58:47.515599Z] DEBUG - 🔄 开始处理订单: perf_order_002
[2025-07-25T10:58:47.516598Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:47.625437Z] DEBUG - 🔄 开始处理订单: perf_order_003
[2025-07-25T10:58:47.626487Z] DEBUG - ✅ 订单处理成功: perf_order_001 - 耗时: 0.20s, 索引: 1
[2025-07-25T10:58:47.627485Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:47.733690Z] DEBUG - ✅ 订单处理成功: perf_order_002 - 耗时: 0.22s, 索引: 2
[2025-07-25T10:58:47.736712Z] DEBUG - 🔄 开始处理订单: perf_order_004
[2025-07-25T10:58:47.737761Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:47.844257Z] DEBUG - ✅ 订单处理成功: perf_order_003 - 耗时: 0.22s, 索引: 3
[2025-07-25T10:58:47.845254Z] DEBUG - 🔄 开始处理订单: perf_order_005
[2025-07-25T10:58:47.845254Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:47.937762Z] DEBUG - ✅ 订单处理成功: perf_order_004 - 耗时: 0.20s, 索引: 4
[2025-07-25T10:58:47.938761Z] DEBUG - 🔄 开始处理订单: perf_order_006
[2025-07-25T10:58:47.938761Z] DEBUG - ⏱️ 频率限制等待: 0.10秒
[2025-07-25T10:58:48.046205Z] DEBUG - 🔄 开始处理订单: perf_order_007
[2025-07-25T10:58:48.047208Z] DEBUG - ✅ 订单处理成功: perf_order_005 - 耗时: 0.20s, 索引: 5
[2025-07-25T10:58:48.141627Z] DEBUG - ✅ 订单处理成功: perf_order_006 - 耗时: 0.20s, 索引: 6
[2025-07-25T10:58:48.265086Z] DEBUG - ✅ 订单处理成功: perf_order_007 - 耗时: 0.22s, 索引: 7
[2025-07-25T10:58:48.265086Z] INFO - 🎯 并发处理完成 - 总订单数: 8, 成功数: 8, 失败数: 0, 成功率: 100.0%, 总耗时: 0.95秒, 平均耗时: 0.12秒/订单, 异常类型: 无异常
[2025-07-25T11:08:36.294389Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T11:10:40.138831Z] DEBUG - Using proactor: IocpProactor
[2025-07-25T11:10:40.141831Z] INFO - RPA任务开始
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
  Step: task_start
[2025-07-25T11:10:40.142831Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
  Step: config_load
[2025-07-25T11:10:40.142831Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
  Step: config_load
[2025-07-25T11:10:40.143831Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test_auth_manager
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T11:10:40.143831Z] INFO - 通用Token提取器初始化完成
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
[2025-07-25T11:10:40.143831Z] INFO - 用户信息提取器初始化完成
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
[2025-07-25T11:10:40.143831Z] INFO - ✅ 通用认证管理器初始化完成 - 业务类型: shop_account_info, 脚本: test_auth_manager
  Task ID: 5ad0f96c-c409-4a2e-9e94-3aa353292658
  Business: shop_account_info.test_auth_manager
[2025-07-25T11:10:40.144831Z] INFO - RPA任务开始
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
  Step: task_start
[2025-07-25T11:10:40.145831Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
  Step: config_load
[2025-07-25T11:10:40.145831Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
  Step: config_load
[2025-07-25T11:10:40.146831Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test_cache
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T11:10:40.146831Z] INFO - 通用Token提取器初始化完成
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
[2025-07-25T11:10:40.147831Z] INFO - 用户信息提取器初始化完成
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
[2025-07-25T11:10:40.147831Z] INFO - ✅ 通用认证管理器初始化完成 - 业务类型: shop_account_info, 脚本: test_cache
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
[2025-07-25T11:10:40.147831Z] INFO - 🗑️ 认证缓存已清除
  Task ID: bf5215d1-dcc4-49de-bdd3-fe88943003c8
  Business: shop_account_info.test_cache
[2025-07-25T11:10:40.148831Z] INFO - RPA任务开始
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
  Step: task_start
[2025-07-25T11:10:40.148831Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
  Step: config_load
[2025-07-25T11:10:40.149831Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
  Step: config_load
[2025-07-25T11:10:40.149831Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test_distributor
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T11:10:40.150831Z] INFO - 通用Token提取器初始化完成
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
[2025-07-25T11:10:40.150831Z] INFO - 用户信息提取器初始化完成
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
[2025-07-25T11:10:40.150831Z] INFO - ✅ 通用认证管理器初始化完成 - 业务类型: shop_account_info, 脚本: test_distributor
  Task ID: 0323dd98-0ccf-4ff2-b80a-75c1bda6e0f2
  Business: shop_account_info.test_distributor
[2025-07-25T11:10:40.151831Z] INFO - RPA任务开始
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
  Step: task_start
[2025-07-25T11:10:40.151831Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
  Step: config_load
[2025-07-25T11:10:40.152831Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
  Step: config_load
[2025-07-25T11:10:40.152831Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test_conversion
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T11:10:40.152831Z] INFO - 通用Token提取器初始化完成
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
[2025-07-25T11:10:40.153831Z] INFO - 用户信息提取器初始化完成
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
[2025-07-25T11:10:40.153831Z] INFO - ✅ 通用认证管理器初始化完成 - 业务类型: shop_account_info, 脚本: test_conversion
  Task ID: 06a85d12-fd10-4ae8-9b36-606b767356c4
  Business: shop_account_info.test_conversion
[2025-07-25T11:10:40.154831Z] INFO - RPA任务开始
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
  Step: task_start
[2025-07-25T11:10:40.154831Z] INFO - 亿迈系统HTTP客户端基类初始化完成
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
  Details:
    base_url: https://dcmmaster.yibainetwork.com
    request_timeout: 60
    max_retries: 3
[2025-07-25T11:10:40.154831Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
  Step: config_load
[2025-07-25T11:10:40.155945Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
  Step: config_load
[2025-07-25T11:10:40.156458Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test_integration
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T11:10:40.157458Z] INFO - 通用Token提取器初始化完成
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
[2025-07-25T11:10:40.157458Z] INFO - 用户信息提取器初始化完成
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
[2025-07-25T11:10:40.157458Z] INFO - ✅ 通用认证管理器初始化完成 - 业务类型: shop_account_info, 脚本: test_integration
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
[2025-07-25T11:10:40.158457Z] INFO - ✅ 店铺账户信息API客户端初始化完成
  Task ID: 49eb7330-4cfd-4653-a9c2-5d070d616c8b
  Business: shop_account_info.test_integration
  Details:
    business_type: shop_account_info
    script_name: test_integration
    base_url: https://dcmmaster.yibainetwork.com
[2025-07-25T11:10:40.158457Z] INFO - RPA任务开始
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
  Step: task_start
[2025-07-25T11:10:40.160456Z] DEBUG - 从环境变量获取亿迈用户名
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
  Step: config_load
[2025-07-25T11:10:40.160456Z] DEBUG - 从环境变量获取亿迈密码
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
  Step: config_load
[2025-07-25T11:10:40.160456Z] INFO - 异步亿迈登录管理器初始化完成
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
  Step: yimai_login_init
  Details:
    business_type: shop_account_info
    script_name: test_network
    has_username: True
    has_password: True
    base_url: https://dcmmaster.yibainetwork.com/
    cache_duration: 300
[2025-07-25T11:10:40.161456Z] INFO - 通用Token提取器初始化完成
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
[2025-07-25T11:10:40.161456Z] INFO - 用户信息提取器初始化完成
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
[2025-07-25T11:10:40.161456Z] INFO - ✅ 通用认证管理器初始化完成 - 业务类型: shop_account_info, 脚本: test_network
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
[2025-07-25T11:10:40.161456Z] DEBUG - ✅ 成功解析登录响应数据，包含字段: ['code', 'data']
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
[2025-07-25T11:10:40.161456Z] INFO - ✅ 从登录响应中提取到用户ID: user123
  Task ID: 439c42e1-7849-4f7b-80a3-9c5c1889fca4
  Business: shop_account_info.test_network
