"""
通用认证管理器

基于屏蔽与解屏蔽脚本的成熟实现，提供一站式的登录验证和用户信息提取服务。
主要功能：
1. 统一的登录验证流程
2. 多渠道用户信息提取（响应数据、网络历史、JWT Token）
3. Token提取和管理
4. 网络拦截和响应处理
5. 缓存和重用机制

使用方式：
    auth_manager = UniversalAuthManager(logger, business_type, script_name)
    tokens, user_info = await auth_manager.authenticate_and_extract_all(driver)
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from app.shared.clients.user_info_extractor import UserInfoExtractor
from app.shared.clients.token_extractor import TokenExtractor
from app.utils.yimai_login_async import AsyncYimaiLoginManager
from app.core.web_driver.base_driver import BaseWebDriver


class UniversalAuthManager:
    """
    通用认证管理器
    
    基于block_manager的成熟实现，提供一站式认证服务
    """
    
    def __init__(self, logger: logging.Logger = None, business_type: str = None, script_name: str = None):
        """
        初始化通用认证管理器
        
        Args:
            logger: 日志记录器
            business_type: 业务类型
            script_name: 脚本名称
        """
        self.logger = logger or logging.getLogger(__name__)
        self.business_type = business_type
        self.script_name = script_name
        
        # 初始化组件
        self.yimai_login_manager = AsyncYimaiLoginManager(
            self.logger, self.business_type, self.script_name
        )
        self.token_extractor = TokenExtractor(self.logger)
        self.user_info_extractor = UserInfoExtractor(self.logger)
        
        # 认证状态缓存
        self.login_response_data = None
        self.user_id = None
        self.cached_tokens = None
        self.cached_user_info = None
        self.last_auth_time = None
        
        # 网络拦截状态
        self._network_intercepted = False
        
        self.logger.info(f"✅ 通用认证管理器初始化完成 - 业务类型: {business_type}, 脚本: {script_name}")
    
    async def authenticate_and_extract_all(self, driver: BaseWebDriver, 
                                          force_refresh: bool = False) -> Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]:
        """
        一站式认证和信息提取服务
        
        Args:
            driver: Web驱动实例
            force_refresh: 是否强制刷新（忽略缓存）
            
        Returns:
            Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]: Token字典和用户信息字典
        """
        try:
            # 检查缓存（如果不强制刷新）
            if not force_refresh and self._is_cache_valid():
                self.logger.info("✅ 使用缓存的认证信息")
                return self.cached_tokens, self.cached_user_info
            
            self.logger.info("🚀 开始一站式认证和信息提取")
            
            # 步骤1: 设置网络拦截
            await self._setup_network_interception(driver)
            
            # 步骤2: 确保登录状态
            self.logger.info("🔐 步骤1: 确保登录状态")
            login_success = await self.yimai_login_manager.ensure_login(driver)
            if not login_success:
                self.logger.error("❌ 登录失败，无法继续执行")
                return None, None
            
            # 步骤3: 提取Token信息
            self.logger.info("🔑 步骤2: 提取Token信息")
            tokens = await self.token_extractor.extract_tokens_from_page(driver.page)
            if not tokens or not tokens.get('jwt_token'):
                self.logger.error("❌ Token提取失败，无法继续执行")
                return None, None
            
            # 步骤4: 多渠道提取用户信息（基于block_manager的成熟逻辑）
            self.logger.info("👤 步骤3: 多渠道提取用户信息")
            user_info_dict = await self._extract_user_info_multi_channel(driver, tokens)
            if not user_info_dict or not user_info_dict.get('uid'):
                self.logger.error("❌ 用户信息提取失败，无法继续执行")
                return None, None
            
            # 缓存认证信息
            self.cached_tokens = tokens
            self.cached_user_info = user_info_dict
            self.last_auth_time = datetime.now()
            
            self.logger.info(f"✅ 一站式认证完成 - 用户ID: {user_info_dict.get('uid')}, "
                           f"分销商ID: {user_info_dict.get('distributor_id')}, "
                           f"账户: {user_info_dict.get('account')}, "
                           f"JWT Token: {'有' if tokens.get('jwt_token') else '无'}")
            
            return tokens, user_info_dict
            
        except Exception as e:
            self.logger.error(f"❌ 一站式认证异常: {str(e)}")
            return None, None
    
    async def _setup_network_interception(self, driver: BaseWebDriver):
        """
        设置网络请求拦截（基于block_manager的实现）
        
        Args:
            driver: Web驱动实例
        """
        try:
            if not hasattr(driver, 'page') or not driver.page:
                self.logger.warning("页面对象不可用，跳过网络拦截设置")
                return
            
            if self._network_intercepted:
                self.logger.debug("网络拦截已设置，跳过重复设置")
                return
            
            self.logger.info("🌐 设置网络请求拦截")
            
            # 设置响应监听器（基于block_manager的逻辑）
            driver.page.on('response', self._handle_response)
            self._network_intercepted = True
            
            self.logger.debug("✅ 网络请求拦截已启用")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 设置网络拦截时异常: {e}")
    
    async def _handle_response(self, response):
        """
        网络响应处理器（基于block_manager的成熟实现）
        
        Args:
            response: 响应对象
        """
        try:
            url = response.url
            status = response.status
            
            if status == 200:
                # 记录响应数据，用于后续提取用户信息（完全基于block_manager逻辑）
                if 'accountLogin' in url or 'login/login' in url:
                    try:
                        text = await response.text()
                        self.login_response_data = json.loads(text)
                        self.logger.debug(f"✅ 成功解析登录响应数据，包含字段: {list(self.login_response_data.keys())}")
                        
                        # 检查是否包含用户ID（完全基于block_manager逻辑）
                        if self.login_response_data.get('code') == 200 and 'data' in self.login_response_data:
                            data = self.login_response_data['data']
                            if isinstance(data, dict) and 'account_data' in data:
                                account_data = data['account_data']
                                if isinstance(account_data, dict) and 'id' in account_data:
                                    user_id = account_data['id']
                                    self.user_id = user_id
                                    self.logger.info(f"✅ 从登录响应中提取到用户ID: {user_id}")
                    except Exception as e:
                        self.logger.error(f"❌ 解析登录响应数据异常: {str(e)}")
                
        except Exception as e:
            self.logger.warning(f"⚠️ 网络响应处理器异常: {e}")
    
    async def _extract_user_info_multi_channel(self, driver: BaseWebDriver, tokens: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        多渠道提取用户信息（完全基于block_manager的成熟逻辑）
        
        Args:
            driver: Web驱动实例
            tokens: Token字典
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息字典
        """
        try:
            user_info = None
            
            # 渠道1: 使用已提取的用户ID（基于block_manager逻辑）
            if hasattr(self, 'user_id') and self.user_id:
                self.logger.info(f"✅ 使用已提取的用户信息，用户ID: {self.user_id}")
                from app.shared.clients.user_info_extractor import UserInfo
                user_info = UserInfo(user_id=self.user_id)
            else:
                # 渠道2: 尝试从登录响应数据中提取（基于block_manager逻辑）
                if hasattr(self, 'login_response_data') and self.login_response_data:
                    self.logger.info("🔍 尝试从已保存的登录响应中提取用户信息")
                    user_info = await self.user_info_extractor.extract_user_info_from_response_data(self.login_response_data)
                    
                # 渠道3: 如果仍然没有提取到，尝试从网络历史记录中提取（基于block_manager逻辑）
                if not user_info or not user_info.user_id:
                    self.logger.info("🔍 尝试从网络历史记录中提取用户信息")
                    user_info = await self.user_info_extractor.extract_user_info_from_network_history(driver.page)
                    
                # 渠道4: 如果仍然没有提取到，尝试从JWT Token中提取（基于block_manager逻辑）
                if (not user_info or not user_info.user_id) and 'jwt_token' in tokens:
                    self.logger.info("🔍 尝试从JWT Token中提取用户信息")
                    user_info = await self.user_info_extractor.extract_user_info_from_jwt_token(tokens['jwt_token'])
            
            if not user_info or not user_info.user_id:
                self.logger.error("❌ 所有渠道都未能提取到有效的用户信息")
                return None
            
            # 转换为字典格式并增强distributor_id提取
            user_info_dict = self._convert_user_info_to_dict(user_info)
            
            # 增强distributor_id提取（如果缺失）
            if not user_info_dict.get('distributor_id') and self.login_response_data:
                distributor_id = self._extract_distributor_id_from_response(self.login_response_data)
                if distributor_id:
                    user_info_dict['distributor_id'] = distributor_id
                    self.logger.info(f"✅ 从登录响应中补充distributor_id: {distributor_id}")
            
            self.logger.info(f"✅ 多渠道用户信息提取成功 - 用户ID: {user_info_dict.get('uid')}, "
                           f"分销商ID: {user_info_dict.get('distributor_id')}, "
                           f"账户: {user_info_dict.get('account')}, "
                           f"账户名: {user_info_dict.get('account_name')}")
            
            return user_info_dict
            
        except Exception as e:
            self.logger.error(f"❌ 多渠道用户信息提取异常: {str(e)}")
            return None
    
    def _convert_user_info_to_dict(self, user_info_obj) -> Dict[str, Any]:
        """
        将UserInfo对象转换为字典格式
        
        Args:
            user_info_obj: UserInfo对象
            
        Returns:
            Dict[str, Any]: 用户信息字典
        """
        return {
            'uid': user_info_obj.user_id,
            'distributor_id': user_info_obj.distributor_id,
            'account': user_info_obj.account,
            'account_name': user_info_obj.account_name,
            'authority_id': getattr(user_info_obj, 'authority_id', None),
            'distributor_code': getattr(user_info_obj, 'distributor_code', None),
            'is_admin': getattr(user_info_obj, 'is_admin', None),
            'is_master': getattr(user_info_obj, 'is_master', None),
            'level_number': getattr(user_info_obj, 'level_number', None),
            'level_name': getattr(user_info_obj, 'level_name', None),
            'session_id': getattr(user_info_obj, 'session_id', None),
            'service_provider_id': getattr(user_info_obj, 'service_provider_id', None)
        }
    
    def _extract_distributor_id_from_response(self, response_data: Dict[str, Any]) -> Optional[str]:
        """
        从登录响应中提取distributor_id
        
        Args:
            response_data: 登录响应数据
            
        Returns:
            Optional[str]: distributor_id
        """
        try:
            # 尝试多个可能的路径
            paths = [
                ['data', 'account_data', 'distributor_id'],
                ['data', 'distributor_id'],
                ['account_data', 'distributor_id'],
                ['distributor_id']
            ]
            
            for path in paths:
                current = response_data
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        current = None
                        break
                
                if current is not None:
                    return str(current)
            
            return None
            
        except Exception as e:
            self.logger.debug(f"提取distributor_id异常: {e}")
            return None
    
    def _is_cache_valid(self) -> bool:
        """
        检查缓存是否有效
        
        Returns:
            bool: 缓存是否有效
        """
        if not all([self.cached_tokens, self.cached_user_info, self.last_auth_time]):
            return False
        
        # 缓存有效期：30分钟
        from datetime import timedelta
        cache_duration = timedelta(minutes=30)
        
        return (datetime.now() - self.last_auth_time) < cache_duration
    
    def clear_cache(self):
        """清除认证缓存"""
        self.cached_tokens = None
        self.cached_user_info = None
        self.last_auth_time = None
        self.login_response_data = None
        self.user_id = None
        self.logger.info("🗑️ 认证缓存已清除")
    
    def get_cached_auth_info(self) -> Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]:
        """
        获取缓存的认证信息
        
        Returns:
            Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]: Token字典和用户信息字典
        """
        if self._is_cache_valid():
            return self.cached_tokens, self.cached_user_info
        return None, None
