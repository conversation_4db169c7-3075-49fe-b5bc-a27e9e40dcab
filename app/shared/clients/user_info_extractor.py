"""
用户信息提取器

从网络请求中提取用户信息的工具：
1. 从登录响应中提取
2. 从Playwright页面网络请求中提取
3. 提供静态方法供简单场景使用
"""

import logging
import json
import re
from typing import Dict, Any, Optional, List
from playwright.async_api import Page, Request, Response

from app.shared.models.user_info import UserInfo


class UserInfoExtractor:
    """
    用户信息提取器
    
    从网络请求中提取用户信息的工具
    """
    
    def __init__(self, logger: logging.Logger = None, config: Dict[str, Any] = None):
        """
        初始化用户信息提取器
        
        Args:
            logger: 日志记录器
            config: 配置信息
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.config = self._get_default_config()
        
        # 合并自定义配置
        if config:
            self.config.update(config)
        
        self.logger.info("用户信息提取器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 登录接口URL匹配模式
            'login_url_patterns': [
                r'.*\/login\/accountLogin.*',
                r'.*\/login\/login.*',
                r'.*\/user\/getUserInfo.*'
            ],
            
            # 响应状态码
            'success_code': 200,
            
            # 请求方法
            'request_methods': ['POST', 'GET']
        }
    
    async def extract_user_info_from_page(self, page: Page) -> Optional[UserInfo]:
        """
        从页面请求中提取用户信息
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[UserInfo]: 用户信息对象，如果提取失败则返回None
        """
        try:
            self.logger.info("开始从页面请求中提取用户信息")
            
            # 获取最近的登录响应
            login_response = await self._get_latest_login_response(page)
            if not login_response:
                self.logger.warning("未找到登录响应")
                return None
            
            # 解析响应
            response_data = await self._parse_response(login_response)
            if not response_data:
                self.logger.warning("登录响应解析失败")
                return None
            
            # 创建用户信息对象
            user_info = UserInfo.from_login_response(response_data)
            
            self.logger.info("用户信息提取成功", extra_data={
                'account': user_info.account,
                'account_name': user_info.account_name,
                'user_id': user_info.user_id
            })
            
            return user_info
            
        except Exception as e:
            self.logger.error(f"从页面提取用户信息异常: {e}")
            return None
    
    async def extract_user_info_from_response_data(self, response_data: Dict[str, Any]) -> Optional[UserInfo]:
        """
        从已有的响应数据中提取用户信息
        
        Args:
            response_data: 已经解析的响应数据（JSON对象）
            
        Returns:
            Optional[UserInfo]: 用户信息对象，如果提取失败则返回None
        """
        try:
            self.logger.info("开始从响应数据中提取用户信息")
            
            if not response_data:
                self.logger.warning("响应数据为空")
                return None
            
            # 使用_extract_from_response方法提取用户信息
            user_info = self._extract_from_response(response_data)
            
            if not user_info or not user_info.user_id:
                self.logger.warning("未能从响应数据中提取到有效的用户信息")
                # 尝试从其他字段中提取
                user_info = self._extract_from_alternative_fields(response_data)
            
            if user_info and (user_info.user_id or user_info.account):
                self.logger.info(f"用户信息提取成功: 用户ID={user_info.user_id}, 账号={user_info.account}")
                return user_info
            else:
                self.logger.warning("未能提取到用户信息")
                return None
                
        except Exception as e:
            self.logger.error(f"从响应数据提取用户信息异常: {str(e)}")
            return None
    
    def _extract_from_alternative_fields(self, response_data: Dict[str, Any]) -> Optional[UserInfo]:
        """
        从替代字段中提取用户信息
        
        Args:
            response_data: 响应数据
            
        Returns:
            Optional[UserInfo]: 用户信息对象
        """
        try:
            # 尝试从不同的字段结构中提取
            data = response_data.get('data', {})
            
            # 情况1: 直接在data中有用户信息
            if isinstance(data, dict) and ('uid' in data or 'userId' in data or 'id' in data):
                return UserInfo(
                    user_id=data.get('uid') or data.get('userId') or data.get('id'),
                    account=data.get('account') or data.get('userName') or data.get('username'),
                    account_name=data.get('accountName') or data.get('realName') or data.get('name'),
                    session_id=data.get('session_id') or data.get('sessionId'),
                    service_provider_id=data.get('serviceProviderId')
                )
            
            # 情况2: 在data.userInfo中有用户信息
            user_info = data.get('userInfo') or data.get('user_info')
            if isinstance(user_info, dict):
                return UserInfo(
                    user_id=user_info.get('uid') or user_info.get('userId') or user_info.get('id'),
                    account=user_info.get('account') or user_info.get('userName') or user_info.get('username'),
                    account_name=user_info.get('accountName') or user_info.get('realName') or user_info.get('name'),
                    session_id=data.get('session_id') or data.get('sessionId'),
                    service_provider_id=data.get('serviceProviderId')
                )
            
            # 情况3: JWT Token中可能包含用户信息
            jwt_token = response_data.get('token') or response_data.get('jwt_token')
            if jwt_token and isinstance(jwt_token, str):
                # 尝试解析JWT Token
                try:
                    import base64
                    parts = jwt_token.split('.')
                    if len(parts) >= 2:
                        # 解码payload部分
                        padding = '=' * (4 - len(parts[1]) % 4)
                        payload = json.loads(base64.b64decode(parts[1] + padding).decode('utf-8'))
                        return UserInfo(
                            user_id=payload.get('uid') or payload.get('userId') or payload.get('sub'),
                            account=payload.get('account') or payload.get('userName') or payload.get('username'),
                            account_name=payload.get('accountName') or payload.get('name')
                        )
                except Exception:
                    pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"从替代字段提取用户信息异常: {e}")
            return None
    
    async def _get_latest_login_response(self, page: Page) -> Optional[Response]:
        """
        获取最近的登录响应
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[Response]: 登录响应对象
        """
        try:
            # 创建响应监听器
            login_response = None
            
            def handle_response(response):
                nonlocal login_response
                
                # 检查URL是否匹配登录接口
                url = response.url
                if any(re.match(pattern, url) for pattern in self.config['login_url_patterns']):
                    # 检查状态码
                    if response.status == self.config['success_code']:
                        login_response = response
            
            # 添加响应监听器
            page.on('response', handle_response)
            
            # 等待页面加载完成
            await page.wait_for_load_state('networkidle')
            
            # 移除监听器
            page.remove_listener('response', handle_response)
            
            return login_response
            
        except Exception as e:
            self.logger.error(f"获取登录响应异常: {e}")
            return None
    
    async def extract_user_info_from_network_history(self, page: Page) -> Optional[UserInfo]:
        """
        从网络历史记录中提取用户信息
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[UserInfo]: 用户信息对象
        """
        try:
            self.logger.info("开始从网络历史记录中提取用户信息")
            
            # 遍历最近的响应
            login_urls = [
                'login/accountLogin',
                'login/login',
                'user/getUserInfo'
            ]
            
            # 尝试从页面请求历史中查找
            try:
                # 获取页面请求历史
                requests = await page.evaluate("""() => {
                    return window.performance.getEntries()
                        .filter(entry => entry.initiatorType === 'xmlhttprequest')
                        .map(entry => ({
                            url: entry.name,
                            duration: entry.duration
                        }));
                }""")
                
                for req in requests:
                    url = req.get('url', '')
                    if any(login_url in url for login_url in login_urls):
                        self.logger.info(f"找到可能的登录请求: {url}")
                        # 这里我们无法直接获取响应内容，但可以记录找到了相关请求
            except Exception as e:
                self.logger.warning(f"获取性能条目异常: {str(e)}")
            
            # 尝试从响应数据中提取用户信息
            if hasattr(page, 'login_response_data') and page.login_response_data:
                user_info = self._extract_from_response(page.login_response_data)
                if user_info:
                    self.logger.info(f"从页面存储的登录响应中提取到用户信息: {user_info.user_id}")
                    return user_info
            
            self.logger.warning("网络历史记录中未找到登录响应")
            return None
        except Exception as e:
            self.logger.error(f"从网络历史记录提取用户信息异常: {str(e)}")
            return None
    
    async def _parse_response(self, response: Response) -> Dict[str, Any]:
        """
        解析响应
        
        Args:
            response: 响应对象
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        try:
            # 获取响应文本
            text = await response.text()
            
            # 尝试解析JSON
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                self.logger.warning(f"响应不是有效的JSON: {text[:100]}...")
                return {}
                
        except Exception as e:
            self.logger.error(f"解析响应异常: {e}")
            return {}
    
    @staticmethod
    async def extract_from_response_json(response_json: Dict[str, Any]) -> Optional[UserInfo]:
        """
        从响应JSON中提取用户信息（静态方法）
        
        Args:
            response_json: 响应JSON数据
            
        Returns:
            Optional[UserInfo]: 用户信息对象，如果提取失败则返回None
        """
        try:
            # 检查响应格式
            if not response_json or not isinstance(response_json, dict):
                return None
                
            # 检查响应状态
            if response_json.get('code') != 200 and response_json.get('code') != "200":
                return None
                
            # 创建用户信息对象
            return UserInfo.from_login_response(response_json)
            
        except Exception as e:
            logging.error(f"从响应JSON提取用户信息异常: {e}")
            return None
    
    @staticmethod
    async def extract_from_response_text(response_text: str) -> Optional[UserInfo]:
        """
        从响应文本中提取用户信息（静态方法）
        
        Args:
            response_text: 响应文本
            
        Returns:
            Optional[UserInfo]: 用户信息对象，如果提取失败则返回None
        """
        try:
            # 尝试解析JSON
            try:
                response_json = json.loads(response_text)
                return await UserInfoExtractor.extract_from_response_json(response_json)
            except json.JSONDecodeError:
                logging.warning(f"响应不是有效的JSON: {response_text[:100]}...")
                return None
                
        except Exception as e:
            logging.error(f"从响应文本提取用户信息异常: {e}")
            return None
    
    @staticmethod
    async def extract_from_network_request(request: Request, response: Response) -> Optional[UserInfo]:
        """
        从网络请求中提取用户信息（静态方法）
        
        Args:
            request: 请求对象
            response: 响应对象
            
        Returns:
            Optional[UserInfo]: 用户信息对象，如果提取失败则返回None
        """
        try:
            # 检查URL是否匹配登录接口
            url = request.url
            if not (
                'login/accountLogin' in url or
                'login/login' in url or
                'user/getUserInfo' in url
            ):
                return None
            
            # 检查状态码
            if response.status != 200:
                return None
            
            # 获取响应文本
            text = await response.text()
            
            # 提取用户信息
            return await UserInfoExtractor.extract_from_response_text(text)
            
        except Exception as e:
            logging.error(f"从网络请求提取用户信息异常: {e}")
            return None
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_updates: 配置更新
        """
        if config_updates:
            self.config.update(config_updates)
            self.logger.info("用户信息提取器配置已更新", extra_data={
                'updated_keys': list(config_updates.keys())
            })
    
    def _extract_from_response(self, response_data: Dict[str, Any]) -> Optional[UserInfo]:
        """
        从响应数据中提取用户信息
        
        Args:
            response_data: 响应数据
            
        Returns:
            Optional[UserInfo]: 用户信息对象
        """
        try:
            # 检查响应状态
            if response_data.get('code') != 200 and response_data.get('code') != "200":
                self.logger.warning(f"响应状态码不是200: {response_data.get('code')}")
                return None
            
            # 获取数据部分
            data = response_data.get('data', {})
            
            # 检查是否有account_data字段
            account_data = None
            if isinstance(data, dict):
                account_data = data.get('account_data', {})
            
            # 如果找到了account_data字段，检查是否有id字段
            if account_data and isinstance(account_data, dict) and 'id' in account_data:
                self.logger.info(f"从account_data中找到用户ID: {account_data['id']}")
                
                # 直接从account_data中提取所有字段
                user_info = UserInfo(
                    user_id=account_data.get('id'),
                    account=account_data.get('account'),
                    account_name=account_data.get('account_name'),
                    authority_id=account_data.get('authorityId'),
                    distributor_id=account_data.get('distributor_id'),
                    distributor_code=account_data.get('distributor_code'),
                    is_admin=account_data.get('is_admin') == '1',
                    is_master=account_data.get('is_master'),
                    level_number=data.get('level_info', {}).get('level_number'),
                    level_name=data.get('level_info', {}).get('level_name'),
                    session_id=data.get('session_id'),
                    service_provider_id=data.get('serviceProviderId')
                )
                
                # 记录详细的提取信息
                self.logger.debug(f"从account_data提取的用户信息: ID={user_info.user_id}, 账号={user_info.account}, 账号名称={user_info.account_name}")
                
                return user_info
            
            # 使用UserInfo类的方法创建用户信息对象
            user_info = UserInfo.from_login_response(response_data)
            
            if user_info and user_info.user_id:
                self.logger.info(f"成功从响应中提取到用户ID: {user_info.user_id}")
                return user_info
            else:
                self.logger.warning("未能从响应中提取到用户ID")
                return None
                
        except Exception as e:
            self.logger.error(f"从响应提取用户信息异常: {str(e)}")
            return None
    
    async def extract_user_info_from_jwt_token(self, jwt_token: str) -> Optional[UserInfo]:
        """
        从JWT Token中提取用户信息
        
        Args:
            jwt_token: JWT Token字符串
            
        Returns:
            Optional[UserInfo]: 用户信息对象
        """
        try:
            if not jwt_token:
                self.logger.warning("JWT Token为空，无法提取用户信息")
                return None
                
            self.logger.info("开始从JWT Token中提取用户信息")
            
            # 解析JWT Token
            import base64
            import json
            
            # JWT格式：header.payload.signature
            parts = jwt_token.split('.')
            if len(parts) != 3:
                self.logger.warning("JWT Token格式不正确")
                return None
            
            # 解析payload部分
            payload = parts[1]
            
            # 添加必要的padding（JWT使用base64url编码）
            padding = 4 - (len(payload) % 4)
            if padding != 4:
                payload += '=' * padding
            
            # 解码
            try:
                decoded_bytes = base64.urlsafe_b64decode(payload)
                payload_data = json.loads(decoded_bytes.decode('utf-8'))
                
                self.logger.debug("成功解析JWT Token payload", extra_data={
                    'available_fields': list(payload_data.keys())
                })
                
                # 尝试多个可能的用户ID字段
                user_id_fields = ['uid', 'user_id', 'id', 'userId', 'sub', 'userinfo', 'aud']
                
                for field in user_id_fields:
                    if field in payload_data:
                        user_id = payload_data[field]
                        # 如果是数字或字符串数字，转换为字符串
                        if isinstance(user_id, (int, str)):
                            user_id_str = str(user_id)
                            self.logger.debug(f"从JWT Token中解析到用户ID: {user_id_str} (来源字段: {field})")
                            
                            # 创建用户信息对象
                            user_info = UserInfo(user_id=user_id_str)
                            
                            # 尝试提取其他用户信息
                            if 'account' in payload_data:
                                user_info.account = payload_data['account']
                            if 'name' in payload_data:
                                user_info.account_name = payload_data['name']
                            if 'distributor_id' in payload_data:
                                user_info.distributor_id = payload_data['distributor_id']
                                
                            return user_info
                            
                # 如果没有找到直接的用户ID字段，尝试从嵌套对象中查找
                if 'userinfo' in payload_data and isinstance(payload_data['userinfo'], dict):
                    userinfo = payload_data['userinfo']
                    for field in user_id_fields:
                        if field in userinfo:
                            user_id = userinfo[field]
                            if isinstance(user_id, (int, str)):
                                user_id_str = str(user_id)
                                self.logger.debug(f"从JWT Token userinfo中解析到用户ID: {user_id_str}")
                                
                                # 创建用户信息对象
                                user_info = UserInfo(user_id=user_id_str)
                                
                                # 尝试提取其他用户信息
                                if 'account' in userinfo:
                                    user_info.account = userinfo['account']
                                if 'name' in userinfo:
                                    user_info.account_name = userinfo['name']
                                if 'distributor_id' in userinfo:
                                    user_info.distributor_id = userinfo['distributor_id']
                                    
                                return user_info
                
                self.logger.warning("JWT Token中未找到用户ID字段")
                return None
                
            except Exception as e:
                self.logger.error(f"解析JWT Token payload异常: {str(e)}")
                return None
                
        except Exception as e:
            self.logger.error(f"从JWT Token提取用户信息异常: {str(e)}")
            return None 